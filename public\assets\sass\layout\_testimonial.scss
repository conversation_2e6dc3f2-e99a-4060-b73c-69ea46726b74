@use "../utilities" as *;

/* START: Testimonial CSS */
.tj-testimonial-section {
	background-color: var(--tj-color-common-white);
	padding-top: 120px;
	padding-bottom: 120px;
	overflow: hidden;

	.sec-heading {
		display: flex;
		align-items: end;
		justify-content: space-between;
		.sec-title {
			margin-bottom: 0;
		}
	}
	.testimonial-pagination {
		margin-top: 60px;
	}
	@media #{$lg} {
		padding-top: 100px;
		padding-bottom: 100px;
	}
	@media #{$md, $sm, $xs} {
		padding-top: 80px;
		padding-bottom: 80px;

		.testimonial-pagination {
			margin-top: 40px;
		}
	}
}

.tj-testimonial-section-two {
	overflow: hidden;
}

.tj-testimonial-section-three {
	background: var(--tj-color-theme-bg);
	overflow: hidden;
}

.testimonial-item {
	background-color: var(--tj-color-theme-bg);
	padding: 35px 40px 40px 0px;

	.testimonial-content {
		display: flex;
		gap: 30px;
		align-items: start;
		padding-inline-start: 40px;
		.testimonial-quote {
			display: inline-flex;
			font-size: 70px;
			color: var(--tj-color-theme-primary);
			line-height: 1;
			transition: all 0.5s ease-in-out;
			i {
				display: inline-flex;
				line-height: 1;
			}
		}
		.desc {
			max-width: 455px;
			width: 100%;
			font-size: 18px;
			font-weight: var(--tj-fw-regular);
			border-bottom: 1px solid var(--tj-color-border-2);
			padding-bottom: 30px;
			line-height: 1.5;

			p {
				&:last-child {
					margin: 0;
				}
			}
		}
	}
	.tj-testimonial-author {
		display: flex;
		flex-wrap: wrap;
		gap: 20px;
		align-items: center;
		margin-top: 20px;
		.author-images {
			max-width: 118px;
			width: 100%;
			height: 106px;
			mask-image: url(../images/shapes/test-shapes.svg);
			mask-repeat: no-repeat;
			mask-size: contain;
			background: var(--tj-color-common-white);
			mask-position: center;
			position: relative;
			z-index: 1;

			img {
				width: 85px;
				height: 85px;
				border-radius: 50%;
				object-fit: cover;
				position: absolute;
				inset-inline-end: 10px;
				top: 50%;
				transform: translateY(-50%);
				z-index: 2;
			}
		}
		.author-rating {
			display: inline-block;
			background: var(--tj-color-common-white);
			padding: 2px 10px 4px;
			margin-bottom: 10px;
			border-radius: 32px;
			.star-ratings {
				color: var(--tj-color-theme-primary);
				-webkit-text-stroke: 1px var(--tj-color-theme-primary);
				.fill-ratings {
					color: var(--tj-color-theme-primary);
				}
				.empty-ratings {
					color: transparent;
				}
			}
		}
		.author-text {
			.author-name {
				letter-spacing: -0.025em;
				margin-bottom: 4px;
			}
			.sub-title {
				display: block;
				line-height: 1;
			}
		}
	}
	&:hover {
		.testimonial-quote {
			transform: rotateY(-360deg);
		}
	}
	@media #{$xl} {
		padding: 35px 15px 40px 0px;
		.testimonial-content {
			.desc {
				max-width: 365px;
				font-size: 17px;
			}
		}
	}
	@media #{$lg} {
		padding: 35px 15px 40px 0px;
		.testimonial-content {
			padding-inline-start: 20px;
			.desc {
				max-width: 315px;
				font-size: 16px;
			}
		}
	}
	@media #{$md, $sm} {
		padding: 35px 15px 40px 0px;
		.testimonial-content {
			padding-inline-start: 20px;
			.desc {
				max-width: 100%;
				font-size: 16px;
			}
		}
	}
	@media #{$xs} {
		padding: 30px 15px 35px 0px;
		.testimonial-content {
			padding-inline-start: 15px;
			gap: 15px;
			.testimonial-quote {
				font-size: 48px;
			}
			.desc {
				max-width: 100%;
				font-size: 16px;
			}
		}
	}
}

.testimonial-images-2 {
	max-width: 550px;
	width: 100%;
	position: relative;
	z-index: 2;
	@media #{$xl} {
		max-width: 500px;
	}
	@media #{$lg} {
		max-width: 440px;
	}
	@media #{$md, $sm, $xs} {
		max-width: 100%;
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.testimonial-funfact {
		position: absolute;
		inset-inline-start: 30px;
		bottom: 30px;
		background: rgba(247, 247, 247, 0.1);
		backdrop-filter: blur(17.5px);
		padding: 35px 30px 35px;
		.number {
			font-family: var(--tj-ff-heading);
			font-size: 48px;
			font-weight: var(--tj-fw-sbold);
			letter-spacing: -0.025em;
			color: var(--tj-color-common-white);
			line-height: 1;
			.odometer {
				font-family: var(--tj-ff-heading);
				line-height: 1;
			}
		}
		.sub-title {
			display: inline-block;
			color: var(--tj-color-common-white-2);
		}
	}
}

.tj-testimonial-slider-two {
	.testimonial-navigation {
		position: absolute;
		inset-inline-end: 0;
		bottom: 30px;
		z-index: 10;
	}
	.shape-image {
		position: absolute;
		bottom: 0;
		inset-inline-end: 0;
		width: 100%;
		img {
			height: 127px;
			width: 100%;
			@media #{$md} {
				height: 115px;
			}
			@media #{$sm, $xs} {
				height: 100px;
			}
		}
	}
}

.testimonial-style-2 {
	.testimonial-content {
		max-width: 630px;
		width: 100%;
		.testimonial-quote {
			color: var(--tj-color-theme-primary);
			line-height: 1;
			font-size: 75px;
			margin-bottom: 25px;
			@media #{$md, $sm, $xs} {
				margin-bottom: 15px;
			}
		}
		.desc {
			font-size: 26px;
			font-family: var(--tj-ff-heading);
			font-weight: var(--tj-fw-medium);
			letter-spacing: -0.04em;
			p {
				&:last-child {
					margin-bottom: 0;
				}
			}
			@media #{$xl, $lg} {
				font-size: 21px;
			}
			@media #{$sm, $xs} {
				font-size: 18px;
			}
		}
		.testimonial-author {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			column-gap: 16px;
			margin-top: 70px;
			position: relative;
			@media #{$md,$sm, $xs} {
				margin-top: 50px;
			}
			.author-images {
				width: 95px;
				height: 95px;
				border-radius: 50%;
				overflow: hidden;
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
				@media #{$sm, $xs} {
					width: 65px;
					height: 65px;
				}
			}
			.author-text {
				.testimonial-rating {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					background: var(--tj-color-theme-bg);
					padding: 2px 10px 4px;
					margin-bottom: 14px;
					border-radius: 32px;
					.star-ratings {
						color: var(--tj-color-theme-primary);
						-webkit-text-stroke: 1px var(--tj-color-theme-primary);
						.fill-ratings {
							color: var(--tj-color-theme-primary);
						}
						.empty-ratings {
							color: var(--tj-color-theme-primary);
						}
					}
				}
				.title {
					margin-bottom: 0;
				}
				.designation {
					display: block;
					color: var(--tj-color-common-black-2);
				}
			}
		}
		@media #{$md, $sm, $xs} {
			max-width: 100%;
		}
	}
}

.testimonial-style-3 {
	position: relative;
	z-index: 1;
	.testimonial-content-box {
		position: relative;
		background-color: var(--tj-color-common-white);
		padding: 55px 30px 35px 110px;
		clip-path: polygon(0 0, 100% 0, 100% 100%, 7% 100%, 0% 90%);
		z-index: 1;
		@media #{$xs} {
			padding: 40px 20px 35px 120px;
		}
		.testimonial-content {
			.desc {
				font-size: 18px;
				max-width: 445px;
				width: 100%;
				margin-bottom: 25px;
				@media #{$xl, $lg} {
					max-width: 100%;
				}
				@media #{$sm, $xs} {
					font-size: 16px;
				}
				p {
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
			.testimonial-author {
				padding-top: 28px;
				border-top: 1px solid var(--tj-color-border-2);
				.testimonial-rating {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					background: var(--tj-color-theme-bg);
					padding: 2px 7px 4px 13px;
					margin-bottom: 14px;
					border-radius: 32px;
					.star-ratings {
						color: var(--tj-color-theme-primary);
						-webkit-text-stroke: 1px var(--tj-color-theme-primary);
						.fill-ratings {
							color: var(--tj-color-theme-primary);
						}
						.empty-ratings {
							color: var(--tj-color-theme-primary);
						}
					}
				}
				.title {
					margin-bottom: 0;
				}
				.designation {
					display: inline-block;
					font-size: 14px;
					color: var(--tj-color-common-black-2);
				}
			}
		}
	}
	.testimonial-infos {
		display: flex;
		flex-wrap: wrap;
		align-items: start;
		position: absolute;
		inset-inline-start: -35px;
		top: 45px;
		z-index: 2;
		@media #{$xs} {
			inset-inline-start: 25px;
		}
		.testimonial-quote {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			width: 65px;
			height: 65px;
			line-height: 1;
			font-size: 36px;
			color: var(--tj-color-common-white);
			background: var(--tj-color-theme-primary);
			border: 2px solid var(--tj-color-theme-bg);
			border-radius: 50%;
			position: relative;
			z-index: 1;
			@media #{$xs} {
				width: 56px;
				height: 56px;
			}
		}
		.testimonial-images {
			width: 65px;
			height: 65px;
			margin-inline-start: -16px;
			@media #{$xs} {
				width: 56px;
				height: 56px;
			}
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 50%;
				filter: grayscale(100%);
			}
		}
	}
	&.swiper-slide {
		@media #{$xs} {
			padding: 0 15px;
		}
	}
}
/* !END: Testimonial CSS */
