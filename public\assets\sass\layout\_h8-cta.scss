@use "../utilities" as *;

/* START: Home 5 CTA CSS */
.h8-cta {
	&-section {
		padding: 50px 0;
		background-color: var(--tj-color-theme-primary);
		position: relative;
		overflow: hidden;
		.thumb {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			inset-inline-start: 0;
			img {
				width: 100%;
				height: 100%;
				mix-blend-mode: multiply;
			}
		}
	}
	&-wrapper {
		display: flex;
		gap: 15px;
		align-items: center;
		justify-content: center;
		z-index: 2;
		position: relative;
		.sec-title {
			color: var(--tj-color-common-white);
			margin-bottom: 0;
			a {
				display: inline-flex;
				position: relative;
				overflow: hidden;
				&::after {
					content: "";
					position: absolute;
					bottom: 5px;
					inset-inline-end: 0;
					width: 100%;
					height: 2px;
					background-color: var(--tj-color-common-white);
				}
				&:hover {
					&::after {
						animation: linehover 0.8s linear;
					}
				}
			}
		}
		@media #{$md} {
			.sec-title {
				font-size: 38px;
			}
		}
		@media #{$xs} {
			flex-direction: column;
			align-items: center;
			text-align: center;
		}
	}
	&-icon {
		width: 66px;
		height: 66px;
		flex: 0 0 auto;
		background-color: var(--tj-color-theme-dark);
		color: var(--tj-color-common-white);
		font-size: 28px;
		border-radius: 50%;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		transform: translateY(0);
		transition: transform 0.3s ease-in-out;
		&:hover {
			transform: translateY(-5px);
		}
	}
}
