@use "../utilities" as *;

/* START: History CSS */
.timeline {
	position: relative;
	width: 100%;
	padding: 0;
}
.timeline::after {
	content: "";
	position: absolute;
	width: 1px;
	background: var(--tj-color-border-2);
	top: 0;
	bottom: 0;
	inset-inline-start: 50%;
	margin-inline-start: -1px;
}
.timeline-inner {
	position: relative;
	background: inherit;
	width: 50%;
}
.timeline-inner:nth-child(odd) {
	inset-inline-start: 0;
	padding-inline-end: 90px;
	@media #{$lg} {
		padding-inline-end: 60px;
	}
	@media #{$md} {
		padding-inline-end: 40px;
	}
	@media #{$sm, $xs} {
		padding-inline-end: 0;
		padding-inline-start: 40px;
	}
}
.timeline-inner:nth-child(even) {
	inset-inline-start: 50%;
	padding-inline-start: 90px;
	@media #{$lg} {
		padding-inline-start: 60px;
	}
	@media #{$md} {
		padding-inline-start: 40px;
	}
	@media #{$sm, $xs} {
		padding-inline-start: 40px;
	}
}
.timeline-inner::after {
	content: "";
	position: absolute;
	width: 16px;
	height: 16px;
	top: calc(50% - 8px);
	inset-inline-end: -8px;
	background: #ffffff;
	border: 4px solid var(--tj-color-common-black-2);
	border-radius: 16px;
	z-index: 1;
}
.timeline-inner:nth-child(even)::after {
	inset-inline-start: -8px;
}
.timeline-inner .date {
	position: absolute;
	display: inline-block;
	top: calc(50% - 25px);
	text-align: center;
	font-size: 48px;
	line-height: 1;
	font-weight: bold;
	color: var(--tj-color-common-black-2);
	text-transform: uppercase;
	letter-spacing: 1px;
	z-index: 1;
	@media #{$md} {
		font-size: 40px;
	}
	@media #{$sm, $xs} {
		font-size: 24px;
		top: calc(50% - 12px);
	}
}
.timeline-inner:nth-child(odd) .date {
	inset-inline-end: -200px;
	@media #{$lg} {
		inset-inline-end: -170px;
	}
	@media #{$md} {
		inset-inline-end: -130px;
	}
}
.timeline-inner:nth-child(even) .date {
	inset-inline-start: -200px;
	@media #{$lg} {
		inset-inline-start: -170px;
	}
	@media #{$md} {
		inset-inline-start: -130px;
	}
}
.timeline-inner .content {
	padding: 30px 30px;
	background: var(--tj-color-common-white);
	border: 1px solid var(--tj-color-border-2);
	position: relative;
	@media #{$md, $sm, $xs} {
		padding: 20px;
	}
	.top {
		padding-inline-start: 40px;
		position: relative;
		span {
			position: absolute;
			top: 0;
			inset-inline-start: 0;
			font-size: 24px;
			font-family: var(--tj-ff-heading);
			line-height: 1.2;
			font-weight: 600;
			color: var(--tj-color-common-black-3);
			@media #{$md, $sm, $xs} {
				font-size: 20px;
			}
		}
		.title {
			margin-bottom: 15px;
		}
		p {
			margin: 0;
		}
	}
	.bottom {
		margin-top: 30px;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
	}
}
@media (max-width: 767.98px) {
	.timeline {
		display: flex;
		flex-direction: column;
		gap: 60px;
		padding-top: 30px;
	}
	.timeline::after {
		inset-inline-start: 10px;
	}
	.timeline-inner {
		width: 100%;
	}
	.timeline-inner:nth-child(even) {
		inset-inline-start: 0%;
	}
	.timeline-inner:nth-child(odd)::after,
	.timeline-inner:nth-child(even)::after {
		inset-inline-start: 1px;
		top: -38px;
	}
	.timeline-inner:nth-child(odd) .date,
	.timeline-inner:nth-child(even) .date {
		inset-inline-end: auto;
		inset-inline-start: 40px;
		top: -42px;
	}
}

/* !END: History CSS */
