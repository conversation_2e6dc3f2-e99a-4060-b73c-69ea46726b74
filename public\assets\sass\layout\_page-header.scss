@use "../utilities" as *;

/* START: Page Header CSS */
.tj-page {
	&-header {
		padding-top: 287px;
		padding-bottom: 170px;
		background-position: center;
		background-size: cover;
		background-repeat: no-repeat;
		background-color: var(--tj-color-theme-dark);
		position: relative;
		overflow: hidden;
		z-index: 2;
		@media #{$xl} {
			padding-top: 230px;
			padding-bottom: 120px;
		}
		@media #{$lg} {
			padding-top: 210px;
			padding-bottom: 100px;
		}
		@media #{$md} {
			padding-top: 210px;
			padding-bottom: 80px;
		}
		@media #{$sm, $xs} {
			padding-top: 140px;
			padding-bottom: 80px;
		}
		&::before {
			position: absolute;
			content: "";
			top: 0;
			inset-inline-start: 0;
			width: 100%;
			height: 100%;
			z-index: -1;
			background-color: rgba(5, 18, 41, 0.8);
		}
	}
	&-title {
		color: var(--tj-color-common-white);
		font-size: 48px;
		margin-bottom: 0;
		@media #{$md} {
			font-size: 42px;
		}
		@media #{$sm, $xs} {
			font-size: 36px;
		}
	}
	&-link {
		margin-top: 25px;
		display: -webkit-inline-box;
		display: -ms-inline-flexbox;
		display: inline-flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		gap: 10px;
		position: relative;
		border-radius: 50px;
		background: rgba(247, 247, 247, 0.1);
		padding: 8px 15px;
		z-index: 2;
		span {
			color: var(--tj-color-common-white);
			font-size: 16px;
			position: relative;
			display: inline-block;
			line-height: 1;
			a {
				&:hover {
					span {
						color: var(--tj-color-theme-primary);
					}
				}
			}
		}
	}
}
/* !END: Page Header CSS */
