@use "../utilities" as *;

/* START: Team CSS */
.tj-team-section {
	overflow: hidden;
	.sec-heading {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: end;
		.sec-text {
			.sec-title {
				margin-bottom: 0;
				line-height: 1;
			}
		}
	}
}
.team-item {
	position: relative;
	min-height: 468px;
	overflow: hidden;
	z-index: 2;
	&::before {
		position: absolute;
		content: "";
		width: 100%;
		height: 100%;
		top: 0;
		inset-inline-start: 0;
		background: linear-gradient(
			180deg,
			rgba(5, 18, 41, 0) 0%,
			var(--tj-color-theme-dark) 84%
		);
		z-index: 3;
	}
	.team-images {
		overflow: hidden;
		img {
			width: 100%;
		}
	}
	.team-content {
		position: absolute;
		bottom: 0;
		width: 100%;
		inset-inline-start: 0;
		padding: 25px 30px;
		background-color: var(--tj-color-theme-dark);
		z-index: 5;
		.team-text {
			.title {
				color: var(--tj-color-common-white);
				letter-spacing: -0.025em;
				margin-bottom: 0;
				a {
					&:hover {
						color: var(--tj-color-theme-primary);
					}
				}
			}
			.sub-title {
				display: inline-block;
				font-size: 14px;
				color: var(--tj-color-common-white-2);
				line-height: 1;
			}
		}
	}
	.team-share {
		position: absolute;
		bottom: 30px;
		inset-inline-end: -30px;
		opacity: 0;
		visibility: hidden;
		@include transition(all 0.4s ease-in-out 0s);
		ul {
			margin: 0;
			padding: 0;
			list-style: none;
			display: flex;
			flex-wrap: wrap;
			flex-direction: column;
			gap: 6px;
			li {
				a {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					width: 24px;
					height: 24px;
					border-radius: 50%;
					font-size: 14px;
					color: var(--tj-color-heading-primary);
					background: rgba(223, 236, 253, 0.4);
					line-height: 1;
					i {
						color: var(--tj-color-heading-primary);
					}
					&:hover {
						color: var(--tj-color-common-white);
						background-color: var(--tj-color-theme-primary);
						i {
							color: var(--tj-color-common-white);
						}
					}
				}
			}
		}
	}

	&:hover {
		.team-share {
			opacity: 1;
			visibility: visible;
			inset-inline-end: 30px;
		}
		.team-images {
			img {
				transform: scale(1.12);
			}
		}
	}
	@media #{$xl} {
		min-height: 420px;
		.team-content {
			padding: 25px 30px;
		}
	}
	@media #{$lg} {
		min-height: 360px;
		.team-content {
			padding: 25px 15px;
		}
	}
	@media #{$md, $sm, $xs} {
		min-height: auto;
	}
}
/* !END: Team CSS */
