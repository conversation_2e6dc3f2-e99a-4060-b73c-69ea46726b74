@use "../utilities" as *;

/*----------------------------------------*/
/*  1. WOOCOMMERCE CSS START
/*----------------------------------------*/
figure {
	margin: 0;
}

.tj-product-details-bottom {
	& .tj-product-details-description {
		& h2 {
			font-size: 26px;
			color: var(--tj-color-heading-primary);
			font-weight: 600;
			margin-bottom: 25px;
		}
	}
}

span.required {
	color: var(--tj-color-text-body);
}
ul.woocommerce-error li {
	color: #ff0004;
	a {
		text-decoration: none;
	}
}
.tj-product-details-description {
	p:last-child {
		margin-bottom: 0;
	}
	p.woocommerce-noreviews {
		margin-bottom: 25px;
	}
	& ol.commentlist {
		padding-inline-start: 0;
		list-style: none;
		& li {
			margin-bottom: 15px;
		}

		& .review {
			& .comment_container {
				overflow: hidden;
				border: 1px solid var(--tj-color-border-2);
				padding: 30px;
				& img {
					float: left;
					overflow: hidden;
					border-radius: 50%;
				}

				& .comment-text {
					overflow: hidden;
					margin-inline-start: 75px;
					& .meta {
						margin-bottom: 5px;
					}
					p {
						margin-bottom: 0;
					}
					.star-rating {
						display: inline-block;
						padding: 3px 4px 3px 8px;
						background-color: var(--tj-color-theme-bg);
						border-radius: 32px;
						&::before {
							position: absolute;
						}
						span {
							position: unset;
						}
					}
				}
			}
		}
	}

	& label {
		display: block;
		font-size: 16px;
	}
	& input:not([type="submit"]),
	& textarea {
		padding: 0px 20px;
		width: 100%;
		height: 48px;
		border: 1px solid var(--tj-color-border-1);
		border-radius: 0;
		outline: 0;
		font-weight: 500;
		font-size: 16px;
		color: var(--tj-color-text-body);
		background-color: transparent;
		&:focus {
			background-color: var(--tj-color-theme-bg);
		}
		&::placeholder {
			color: var(--tj-color-text-body-2);
			font-weight: var(--tj-fw-medium);
		}
		&::-ms-input-placeholder {
			color: var(--tj-color-text-body-2);
			font-weight: var(--tj-fw-medium);
		}
	}

	& textarea {
		padding-top: 20px;
		height: 180px;
		line-height: 1.2;
		padding-bottom: 20px;
		resize: none;
	}
	input[type="checkbox"] {
		width: 17px;
		height: 17px;
		margin-top: 0 !important;
	}
	.form-submit input[type="submit"] {
		font-family: var(--tj-ff-body);
		font-weight: 600;
		font-size: 16px;
		line-height: 1;
		color: var(--tj-color-common-white);
		background-color: var(--tj-color-theme-dark);
		display: inline-block;
		padding: 20px 30px 20px 30px;
		border-radius: 50px;
		&:focus,
		&:hover {
			color: var(--tj-color-common-white);
			background-color: var(--tj-color-theme-primary);
		}
	}

	& .woocommerce-Reviews-title {
		font-family: var(--tj-ff-body);
		font-weight: 600;
		font-size: 24px;
		letter-spacing: -0.02em;
		margin-bottom: 25px;
	}

	.woocommerce-review__author {
		font-weight: 600;
		font-size: 16px;
		line-height: 20px;
		color: var(--tj-color-heading-primary);
		position: relative;
		text-transform: capitalize;
	}

	.woocommerce-review__published-date {
		font-weight: 500;
		font-size: 14px;
		line-height: 18px;
		color: #888888;
	}

	.comment-form-rating {
		flex-direction: column;
		& label {
			font-weight: 500;
			font-size: 16px;
			color: var(--tj-color-text-body);
		}

		& .stars {
			& > span {
				color: var(--tj-color-theme-primary);
			}
		}
		& .nice-select {
			display: none;
		}
		& select {
			display: none;
		}

		& span {
			margin-inline-end: 5px;
			font-size: 16px;
			color: var(--tj-color-text-body);
			font-weight: 500;
			margin-bottom: 5px;
		}
	}

	.comment-reply-title {
		font-family: var(--tj-ff-heading);
		font-weight: 600;
		font-size: 19px;
		letter-spacing: -0.02em;
		margin-bottom: 4px;
		color: var(--tj-color-heading-primary);
	}

	.comment-form-comment {
		& label {
			font-family: var(--tj-ff-body);
			font-weight: 500;
			font-size: 16px;
			color: var(--tj-color-text-body);
			display: inline-block;
			margin-bottom: 20px;
		}
	}

	& .comment-form-author,
	& .comment-form-email {
		width: 48%;
		display: inline-block;
		margin-inline-end: 30px;
		@media (max-width: 1200px) {
			width: 100%;
		}
	}
	& .comment-form-email {
		margin-inline-end: 0;
		width: 49%;
		@media (max-width: 1200px) {
			width: 100%;
		}
	}
}

.tj-product-details-description {
	table tbody > tr:nth-child(odd) > td,
	table tbody > tr:nth-child(odd) > th {
		background-color: inherit;
	}

	& table {
		border: 1px solid var(--tj-color-border-2);
		width: 100%;
		margin-bottom: 0;
		& th,
		& td {
			padding: 0;
			line-height: 1.5;
			vertical-align: middle;
			border: 1px solid var(--tj-color-border-2);
			text-align: start;
			background-color: transparent;
			& p {
				margin-bottom: 0;
			}
		}

		& tr {
			&:not(:last-child) {
				border-bottom: 1px solid var(--tj-color-border-2);
			}
			& th {
				font-size: 16px;
				color: var(--tj-color-heading-primary);
				background-color: transparent !important;
				width: 306px;
				padding: 12px 34px;
				font-weight: 500;
				@media #{$xs} {
					width: 160px;
					padding: 12px 20px;
				}
			}
			& td {
				padding: 12px 34px;
				@media #{$xs} {
					padding: 12px 20px;
				}
				&:first-child {
					font-size: 14px;
					color: var(--tj-color-heading-primary);
					background-color: #f9f9f9;
					width: 306px;
					@media #{$xs} {
						width: 160px;
					}
				}
				&:last-child {
					font-size: 16px;
					color: var(--tj-color-text-body);
				}

				& p {
					margin-bottom: 0;
					font-size: 14px;
				}
			}
		}
	}
}

body {
	.woocommerce-notices-wrapper,
	.woocommerce-message,
	.woocommerce-error,
	.woocommerce-info {
		outline: none !important;
		box-shadow: none !important;
		border-radius: 0;

		&:focus,
		&:hover,
		&:active,
		&:visited,
		&:focus-visible {
			outline: none !important;
			box-shadow: none !important;
			border-radius: 0;
		}
	}
}

.woocommerce-message {
	background: var(--tj-color-theme-bg);
	padding: 15px 20px;
	margin-bottom: 30px;
	border: 1px solid var(--tj-color-border-2);
	color: var(--tj-color-theme-primary);
	& a {
		text-decoration: underline;
		color: var(--tj-color-theme-primary);
		font-weight: 500;
		&:hover {
			color: var(--tj-color-theme-dark);
		}
	}
}

.product-type-variable {
	& .tj-product-details-wrapper .product-percentage-badges {
		transform: translateY(0px);
	}
}

.tj-product-details-wrapper,
.tj-product-details-action-wrapper {
	& .variations_form {
		& table {
			position: relative;
			margin-bottom: 30px;
			& td {
				padding: 12px 12px;
			}
			& tr {
				& .nice-select {
					width: 100%;
					background-color: #f9f9f9;
					border: 1px solid rgba(1, 15, 28, 0.1);
					border-radius: 0;
					font-size: 14px;
					color: var(--tj-color-heading-primary);
					height: 40px;
					line-height: 38px;
					padding: 0 25px;
					min-width: 204px;
					float: none;

					&::after {
						position: absolute;
						content: "\f107";
						top: 50%;
						inset-inline-end: 15px;
						font-family: var(--tj-ff-fontawesome);
						color: var(--tj-color-heading-primary);
						font-weight: 500;
						pointer-events: none;
						-webkit-transition: all 0.15s ease-in-out;
						transition: all 0.15s ease-in-out;
						margin-top: 0;
						transform-origin: center;
						@include transform(translateY(-50%));
						width: auto;
						height: auto;
					}

					&.open {
						&::after {
							@include transform(translateY(-50%) rotate(-180deg));
						}

						& .list {
							@include transform(scale(1) translateY(0px));
						}
					}

					& .list {
						margin-top: 0;
						border-radius: 0;
						transform-origin: center center;
						@include transform(scale(0.9) translateY(0px));
						width: 100%;
						padding: 10px 0;

						& .option {
							line-height: 1.2;
							min-height: inherit;
							padding-top: 5px;
							padding-bottom: 5px;

							&:hover {
								color: var(--tj-color-theme-primary);
							}
						}
					}
				}
			}
		}
		& .disabled {
			opacity: 0.5;
			cursor: not-allowed;
		}

		& .quantity {
			position: relative;
			width: 120px;
		}

		& .reset_variations {
			color: red;
			position: absolute;
			margin-top: 10px;
		}
	}
	& .reset_variations {
		display: none;
	}
	& .single_variation_wrap {
		& .single_variation {
			margin-bottom: 30px;
			& .amount {
				font-weight: 600;
				font-size: 24px;
				color: var(--tj-color-heading-primary);
				margin-inline-start: 1px;
			}

			& .woocommerce-variation-price {
				& span {
					font-weight: 500;
					font-size: 24px;
					letter-spacing: -0.02em;
					color: var(--tj-color-heading-primary);
				}

				& del {
					text-decoration: none;
					& span {
						font-weight: 400;
						font-size: 16px;
						text-decoration-line: line-through;
						color: #767a7d;
					}
				}

				& ins {
					text-decoration: none;
					& span {
						color: var(--tj-color-heading-primary);
					}
				}
			}
		}
	}
}

.woosw-popup {
	transition: all 0.3s;
}

// quick table css here
.woosc-quick-table {
	margin-bottom: 60px;
	& h2 {
		margin-bottom: 25px;
	}
	& table {
		& thead {
			& th {
				padding: 15px;
			}
		}
		& tbody {
			& tr {
				& td {
					z-index: 1;
					& .add_to_cart_button,
					& .product_type_grouped,
					& .product-action-btn {
						font-size: 14px;
						color: var(--tj-color-common-white);
						background-color: var(--tj-color-theme-primary);
						width: 100%;
						padding: 5px 15px;
						display: inline-block;
						text-align: center;
						font-weight: 500;

						& svg {
							@include transform(translateY(-2px));
						}
						& svg,
						& i {
							margin-inline-end: 4px;
						}

						&:hover {
							background-color: var(--tj-color-heading-primary);
							color: var(--tj-color-common-white);
						}
					}

					& p {
						margin-bottom: 0;
					}

					& span {
						font-weight: 500;
						font-size: 11px;
						display: inline-block;
					}

					& del {
						font-family: var(--tj-ff-body);
						font-weight: 500;
						font-size: 16px;
						color: #c2c2d3;
						& .woocommerce-Price-amount,
						& span {
							font-size: 13px;
							color: #c2c2d3;
						}
					}
					& ins {
						text-decoration: none;
						font-family: var(--tj-ff-body);
						font-weight: 500;
						& .woocommerce-Price-amount {
							font-size: 16px;
							color: var(--tj-color-heading-primary);
						}
						& span {
							color: var(--tj-color-heading-primary);
						}
					}

					&:first-child {
						padding-inline-start: 30px;
					}
				}

				&:not(:first-child) {
					& td {
						padding: 15px 10px;
					}
				}

				&:nth-child(2n) {
					background-color: var(--tj-color-theme-bg6);
				}
			}
		}
	}
}
// for group products
.tj-product-details-wrapper {
	& .grouped_form {
		& .tj-product-details-quantity {
			margin-bottom: 0;
		}
		& tbody {
			& tr {
				& td {
					border: 1px solid #e7e8eb;

					&:first-child {
						padding: 0px 15px;
					}
					&:not(:first-child) {
						padding: 15px 15px;
					}

					& span {
						font-family: var(--tj-ff-body);
						font-weight: 600;
						font-size: 16px;
						color: var(--tj-color-text-body);
					}

					& del {
						font-family: var(--tj-ff-body);
						font-weight: 600;
						font-size: 16px;
						color: #c2c2d3;
						& .woocommerce-Price-amount,
						& span {
							font-size: 16px;
							color: #c2c2d3;
						}
					}
					& ins {
						text-decoration: none;
						font-family: var(--tj-ff-body);
						font-weight: 500;
						& .woocommerce-Price-amount {
							font-size: 16px;
							color: var(--tj-color-heading-primary);
						}
						& span {
							color: var(--tj-color-heading-primary);
						}
					}
				}
			}
		}

		& .woocommerce-grouped-product-list {
			margin-bottom: 35px;
		}

		& .woocommerce-grouped-product-list-item__label {
			& a {
				color: var(--tj-color-heading-primary);
				font-weight: 600;
				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}
		}

		& .single_add_to_cart_button {
			padding: 12px 30px;
		}
	}
}

// wishlist css
.woosw-item--time {
	display: none !important;
}

.woosw-item--price {
	& span {
		font-family: var(--tj-ff-body);
		font-weight: 500;
		font-size: 16px;
		color: var(--tj-color-text-body);
	}

	& del {
		font-family: var(--tj-ff-body);
		font-weight: 500;
		font-size: 16px;
		color: var(--tj-color-text-body-2);
		text-decoration: none;
		& .woocommerce-Price-amount,
		& span {
			font-size: 16px;
			color: var(--tj-color-text-body-2);
			text-decoration: line-through;
		}
	}
	& ins {
		text-decoration: none;
		font-family: var(--tj-ff-body);
		font-weight: 500;
		& .woocommerce-Price-amount {
			font-size: 16px;
			color: var(--tj-color-heading-primary);
		}
		& span {
			color: var(--tj-color-heading-primary);
		}
	}
}

.woosw-item--add {
	& .add_to_cart_button,
	& .added_to_cart,
	& .product-action {
		font-weight: 600;
		color: var(--tj-color-heading-primary);
		background-color: #fff;
		display: inline-block;
		text-align: center;
		font-size: 0;
		border: 1px solid #e7e8eb;
		width: 50px;
		height: 50px;
		line-height: 55px;
		&.added_to_cart {
			line-height: 50px;
		}
		&:hover {
			background-color: var(--tj-color-theme-primary);
			color: var(--tj-color-common-white);
		}

		& svg,
		& i {
			font-size: 14px;
			margin-inline-end: 0;
		}

		& svg {
			@include transform(translate(0px, -3px));
		}

		& .product-action-tooltip {
			display: none;
		}
	}

	& .woocommerce a.added_to_cart {
		font-size: 14px;
		&::before {
			color: var(--tj-color-heading-primary);
			transition: all 0.3s ease-out 0s;
		}
		&:hover {
			background-color: var(--tj-color-heading-primary);
			color: var(--tj-color-common-white);
			border-color: var(--tj-color-heading-primary);

			&::before {
				color: var(--tj-color-common-white);
			}
		}
	}

	& .woocommerce a.button.loading {
		&::before {
			color: black;
		}

		&:hover {
			background-color: var(--tj-color-heading-primary);
			color: var(--tj-color-common-white);
			border-color: var(--tj-color-heading-primary);

			&::before {
				color: var(--tj-color-heading-primary);
			}
		}
	}
}

.woosw-item--name {
	& a {
		color: var(--tj-color-heading-primary);
		&:hover {
			color: var(--tj-color-theme-primary);
		}
	}
}

.woosw-popup-content-bot {
	& .woosw-popup-content-bot-inner {
		& a {
			color: var(--tj-color-heading-primary);

			&:hover {
				color: var(--tj-color-theme-primary) !important;
				border-color: var(--tj-color-theme-primary) !important;
			}
		}
	}
}

.woosw-popup.woosw-popup-right
	.woosw-popup-inner
	.woosw-popup-content
	.woosw-popup-content-top {
	background-color: var(--tj-color-common-white);
	border-bottom: 1px solid #e7e8eb;
}

.woosw-popup
	.woosw-popup-inner
	.woosw-popup-content
	.woosw-popup-content-top
	.woosw-popup-close {
	&:hover {
		color: var(--tj-color-theme-primary);
	}
}

.product_meta {
	padding-top: 34px;
	border-top: 1px solid #dadce0;
	margin-top: 32px;
}

/* cart page css */
div.woocommerce {
	& .woocommerce-cart-form {
		& table {
			background: var(--tj-color-common-white);
			border-color: var(--tj-color-border-2);
			border-radius: 0;
			border-style: solid;
			border-width: 1px 0 0 1px;
			text-align: center;
			width: 100%;
			margin-bottom: 0;

			& thead {
				border-bottom: 1px solid var(--tj-color-border-2);
				font-weight: 600;
				& th {
					border-inline-end: 1px solid var(--tj-color-border-2);
					text-align: start;
					font-family: var(--tj-ff-body);
					font-weight: 600;
					font-size: 16px;
					color: var(--tj-color-heading-primary);
					padding: 5px;
					padding-inline-start: 20px;
					&:first-child {
						border-inline-end: 1px solid transparent;
					}
					&.product-subtotal {
						border-inline-end: 1px solid transparent;
					}
				}
			}
			& tbody {
				& tr {
					&:not(:last-child) {
						border-bottom: 1px solid var(--tj-color-border-2);
					}
					td:first-child {
						border-inline-end: 1px solid transparent;
					}

					& td {
						border: 1px solid var(--tj-color-border-2);
						text-align: start;
						padding: 12px;
						padding-inline-start: 20px;
						&.product-thumbnail {
							width: 125px;
							& img {
								width: 100%;
							}
						}

						&.product-name {
							padding-inline-start: 0;
							h5 {
								margin: 0;
								text-align: start;
								@media #{$md, $sm, $xs} {
									font-size: 16px;
								}
							}
							& a {
								text-decoration: none;
								color: var(--tj-color-heading-primary);
								&:hover {
									color: var(--tj-color-theme-primary);
								}
							}
						}

						&.product-price {
							& span {
								font-weight: 400;
								font-size: 16px;
								color: var(--tj-color-text-body);
							}
						}

						&.product-subtotal {
							border-inline-end: 1px solid transparent;
							& span {
								font-weight: 400;
								font-size: 16px;
								color: var(--tj-color-text-body);
							}
						}

						&.product-quantity {
							.tj-product-quantity {
								width: max-content;
								border-radius: 0;
								position: relative;
								& .quantity {
									position: relative;
									background: transparent;
									text-align: center;
									display: flex;
									align-items: center;
									justify-content: center;
									border: 1px solid var(--tj-color-border-2);
								}
								& .tj-cart-plus,
								& .tj-cart-minus {
									display: inline-block;
									width: 44px;
									height: 44px;
									line-height: 44px;
									text-align: center;
									font-size: 16px;
									cursor: pointer;
									transition: all 0.3s ease-in-out;
									&:hover {
										color: var(--tj-color-theme-primary);
									}
								}

								& .tj-cart-input[type="text"] {
									height: 44px;
									line-height: 44px;
									width: 45px;
									background-color: transparent;
									border: 0;
									border-inline-end: 1px solid var(--tj-color-border-2);
									border-inline-start: 1px solid var(--tj-color-border-2);
									border-radius: 0px;
									font-size: 16px;
									color: var(--tj-color-theme-dark);
									text-align: center;
									margin: 0;
									&:focus {
										outline: none;
									}
								}
							}
						}

						&.product-remove {
							text-align: center;
							& a {
								color: var(--tj-color-heading-primary);
								font-size: 22px;
								display: inline-flex;
								align-items: center;
								justify-content: center;
								width: 36px;
								height: 36px;
								border-radius: 50%;
								background-color: transparent;
								border: 1px solid var(--tj-color-border-2);
								text-decoration: none;
								&:is(:hover, :focus) {
									color: var(--tj-color-common-white);
									background-color: var(--tj-color-theme-primary);
									border-color: var(--tj-color-theme-primary);
								}
							}
						}
					}
				}
			}
		}

		& .actions {
			padding-inline-start: 0;
			padding-top: 35px;
			& .coupon {
				& label {
					display: block;
					font-size: 14px;
					color: var(--tj-color-heading-primary);
					font-weight: 500;
					margin-bottom: 7px;
				}

				& #coupon_code {
					padding: 0px 20px;
					width: 225px;
					height: 56px;
					line-height: 56px;
					border: 1px solid var(--tj-color-border-2);
					outline: 0;
					font-weight: 400;
					font-size: 16px;
					color: var(--tj-color-text-body);
					box-shadow: none;
					background-color: transparent;
					border-radius: 0;
					margin-inline-end: 20px;
					transition: all 0.3s cubic-bezier(0.3, 0, 0, 0.3);
					@media #{$xs} {
						width: 100%;
						margin-inline-end: 0;
						margin-bottom: 20px;
					}
					&:focus {
						border-color: var(--tj-color-theme-primary);
					}
				}
			}
			& button {
				&[name="update_cart"]:disabled {
					cursor: not-allowed;
					opacity: 0.2;
				}
			}
		}
	}

	& .select2-container {
		& .select2-selection--single {
			&:focus {
				background-color: var(--tj-color-common-white);
				.select2-selection--single {
					background-color: var(--tj-color-common-white);
					border-color: var(--tj-color-theme-primary);
				}
			}
		}

		& .select2-selection__arrow {
			height: 46px;
			inset-inline-end: 25px;
		}
	}

	& .cart_totals {
		& > h3 {
			margin-bottom: 20px;
		}

		& table {
			width: 100%;
			border: 0;
			& tbody {
				& tr {
					border: 1px solid var(--tj-color-border-2);

					& th {
						border: 0;
						text-align: start;
						font-weight: 600;
						font-size: 16px;
						font-family: var(--tj-ff-heading);
						color: var(--tj-color-heading-primary);
						padding: 15px 20px;
						width: 260px;
					}

					& td {
						border: 0;
						text-align: start;
						font-size: 16px;
						font-weight: 500;
						color: var(--tj-color-heading-primary);
						padding: 15px 20px;
						&[data-title="Shipping"] {
							text-align: start;
							line-height: 1.3;
							padding-inline-start: 15px;
						}

						&[data-title="Total"] {
							font-size: 16px;
							font-weight: 400;
						}

						& .woocommerce-shipping-calculator {
							& .shipping-calculator-button {
								margin-top: 7px;
								display: inline-block;
								color: var(--tj-color-theme-primary);
								&:hover {
									color: var(--tj-color-theme-primary);
								}
							}

							& .shipping-calculator-form {
								margin-top: 14px;
							}

							& input {
								border-width: 1px;
								height: 46px;
								border: 1px solid var(--tj-color-border-2);
								padding: 0 10px;
								width: 100%;
								font-size: 16px;
								background-color: transparent;
								&:focus {
									border-color: var(--tj-color-theme-primary);
									background-color: var(--tj-color-common-white);
								}
							}

							& .select2.select2-container {
								width: 215px !important;
							}

							& .nice-select {
								min-width: 100%;
								font-family: var(--tj-ff-body);
								color: var(--tj-color-heading-primary);
								font-size: 14px;
								border: 0;
								padding-inline-start: 10px;
								padding-inline-end: 50px;
								font-weight: 500;
								border-radius: 0;
								border: 1px solid var(--tj-color-border-2);
								float: none;
								height: 46px;
								line-height: 44px;
								background-color: var(--tj-color-theme-bg);
								color: var(--tj-color-heading-primary);
								width: 100%;

								@media #{$sm, $xs} {
									width: 100%;
									border-radius: 40px;
									border: 1px solid var(--tj-color-border-2);
									height: 50px;
									line-height: 50px;
								}
								&::after {
									inset-inline-end: 25px;
									position: absolute;
									top: 50%;

									@media #{$sm, $xs} {
										inset-inline-end: 25px;
									}
								}

								& .list {
									margin-top: 0;
									border-radius: 0;
									width: 100%;
									padding-bottom: 10px;
									padding-top: 9px;

									@media #{$xs} {
										margin-top: 0;
									}

									& .option {
										line-height: 29px;
										min-height: 29px;
										padding-inline-start: 18px;
										padding-inline-end: 18px;

										&:hover {
											color: var(--tj-color-theme-primary);
										}
									}

									& .option.selected {
										color: var(--tj-color-theme-primary);
									}
								}

								&:focus {
									border-color: var(--tj-color-theme-primary);
									background-color: var(--tj-color-common-white);
								}
							}
						}
					}

					&.order-total {
						& td {
							color: var(--tj-color-heading-primary);
						}
					}
				}
			}
		}
	}

	& .woocommerce-billing-fields__field-wrapper,
	& .woocommerce-shipping-fields__field-wrapper {
		& .select2-selection--single {
			height: 48px;
			line-height: 42px;
			border: 1px solid var(--tj-color-theme-dark);
			background-color: transparent;
			border-radius: 0;

			&:focus {
				background-color: var(--tj-color-common-white);
				.select2-selection--single {
					background-color: var(--tj-color-common-white);
					border-color: var(--tj-color-theme-primary);
				}
			}
		}

		& .select2-selection__arrow {
			height: 46px;
			inset-inline-end: 25px;
		}
	}
	& .woocommerce-shipping-totals {
		& .woocommerce-shipping-methods {
			padding-inline-start: 0;
			& li {
				list-style: none;

				& label {
					margin-inline-start: 5px;

					&:hover {
						cursor: pointer;
					}
				}
			}
		}
	}
}

/* checkout css start */
.woocommerce-checkout {
	& .woocommerce-form-coupon-toggle {
		position: relative;
		& .showcoupon {
			cursor: pointer;
			transition: 0.3s;
			font-weight: 500;
			color: var(--tj-color-theme-primary);
			// text-decoration: underline;
		}
		& .woocommerce-info {
			color: var(--tj-color-theme-primary);
			background-color: var(--tj-color-theme-bg);
			border: 1px solid var(--tj-color-border-2);
			font-size: 16px;
			font-weight: 500;
			margin: 0 0 0;
			padding: 20px 30px;
			position: relative;
			width: 100%;
		}
	}

	& .checkout_coupon {
		& p {
			color: var(--tj-color-heading-primary);
			margin-bottom: 0;

			&:first-child {
				margin-bottom: 10px;
				color: var(--tj-color-text-body);
				font-weight: 500;
			}
		}

		#coupon_code {
			width: 350px;
			height: 56px;
			line-height: 56px;
			border: 1px solid var(--tj-color-theme-dark);
			border-radius: 0;
			background-color: transparent;
			margin-inline-end: 20px;
			&:focus {
				background-color: var(--tj-color-theme-bg);
			}
			@media #{$xs, $sm} {
				margin: 0;
			}
		}
	}

	#customer_form_details {
		border: 0;
		border-radius: 0;

		& .woocommerce-billing-fields {
			& h3 {
				margin-bottom: 20px;
			}
			// & .validate-state {
			// 	& .nice-select {
			// 		&.state_select {
			// 			display: none !important;
			// 		}
			// 	}
			// 	& .state_select {
			// 		display: inline-block !important;
			// 	}
			// }
		}

		& .woocommerce-billing-fields__field-wrapper {
			// & .country_to_state {
			//   display: inline-block !important;
			//   &.nice-select {
			//     display: none !important;
			//   }
			// }
			& .form-row {
				& > label {
					line-height: 1;
					margin-inline-start: 0;
					color: var(--tj-color-text-body);
					font-size: 16px;
					margin-bottom: 15px;
				}

				& input,
				& textarea {
					background: transparent;
					border: 1px solid var(--tj-color-theme-dark);
					border-radius: 0;
					height: 48px;
					padding: 5px 22px;
					width: 100%;
					outline: none;
					-webkit-box-shadow: none;
					-moz-box-shadow: none;
					-ms-box-shadow: none;
					-o-box-shadow: none;
					box-shadow: none;

					&::placeholder {
						color: #6f7172;
					}

					&:focus {
						background-color: var(--tj-color-theme-bg);
					}
				}

				& textarea {
					padding-top: 15px;
					padding-bottom: 15px;
					resize: none;
					line-height: 1.2;
				}
			}

			& .select2-container {
				& .select2-results__options {
					& .select2-results__option {
						font-weight: 400;
						padding-inline-start: 18px;
						padding-inline-end: 29px;
						text-align: start;
					}
				}
				& .select2-selection--single {
					border: 1px solid var(--tj-color-theme-dark);
					margin-bottom: 45px;
				}

				& .select2-selection__rendered {
					height: 48px;
					line-height: 42px;
					padding: 0 22px;
					width: 100%;
					color: var(--tj-color-text-body);
				}
			}

			& .woocommerce-form__input {
				&[type="checkbox"] {
					width: 20px;
					height: 20px;
					border: 1px solid var(--tj-color-border-2);
				}
			}
		}

		& .woocommerce-additional-fields {
			& .woocommerce-additional-fields__field-wrapper {
				& .form-row {
					&.notes {
						margin-bottom: 0;
					}
					& > label {
						margin-top: -6px;
						margin-bottom: 10px;
						margin-inline-start: 0;
					}

					& input {
						background: var(--tj-color-common-white);
						border: 1px solid var(--tj-color-theme-dark);

						&:focus {
							border-color: var(--tj-color-theme-primary);
						}
					}

					& input,
					& textarea {
						border: 1px solid var(--tj-color-theme-dark);
						height: 48px;
						padding: 5px 22px;
						width: 100%;
						outline: 0;
						font-size: 16px;
						border-radius: 0;
						background-color: transparent;
						&::placeholder {
							color: #6f7172;
						}
						&:focus {
							background-color: var(--tj-color-theme-bg);
						}
					}

					& textarea {
						line-height: 1.3;
						resize: none;
						padding-top: 15px;
						padding-bottom: 15px;
						height: 150px;
					}
				}
			}
		}
	}

	.woocommerce-form-login-toggle {
		& .woocommerce-info {
			margin-bottom: 15px;
			display: block;
			border: 1px dashed #aab0b2;
			font-weight: 400;
			font-size: 14px;
			color: var(--tj-color-heading-primary);
			padding: 8px 25px;
			& a {
				color: var(--tj-color-theme-primary);
				text-decoration: underline;
			}
		}
	}

	& .woocommerce-form-login {
		margin-bottom: 30px;
		& p {
			font-size: 14px;
			color: var(--tj-color-heading-primary);
			margin-bottom: 0;

			&:first-child {
				margin-bottom: 15px;
			}
		}

		& label {
			margin: 0;
			display: block;
			height: auto;

			&.woocommerce-form-login__rememberme {
				& input {
					border: 1px solid #e7e8eb;
					background-color: transparent;
					margin-inline-end: 5px;
					height: auto;
				}
				&:hover {
					cursor: pointer;
				}
			}
		}

		& .lost_password {
			margin-top: 15px;
			padding-bottom: 15px;
			& a {
				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}
		}

		input {
			height: 54px;
			background-color: var(--tj-color-common-white);
			border-color: var(--tj-color-common-white);
			margin-bottom: 15px;
			border-width: 1px;
			&:focus {
				border-color: var(--tj-color-theme-primary);
			}
		}

		& .tj-btn {
			padding: 14px 30px;

			&:hover {
				background-color: var(--tj-color-heading-primary);
				color: var(--tj-color-common-white);
			}
		}

		& .woocommerce-button {
			display: inline-block;
			font-size: 16px;
			font-weight: 700;
			color: var(--tj-color-common-white);
			background: var(--tj-color-theme-primary);
			text-align: center;
			font-family: var(--tj-ff-body);
			padding: 14px 30px;
			position: relative;
			z-index: 1;
			overflow: hidden;
			letter-spacing: -0.02em;

			&:hover {
				background-color: var(--tj-color-heading-primary);
			}
		}
	}

	& .tj-free-progress-bar {
		background-color: var(--tj-color-common-white);
	}

	& #ship-to-different-address {
		font-weight: 600;
		font-size: 26px;
		margin-bottom: 35px;
	}
}

/* wishlist css start */

.woosw-list {
	table {
		&.woosw-items {
			width: 100%;
			@media #{ $sm, $xs} {
				border: 1px solid var(--tj-color-border-2);
			}
			tr.woosw-item {
				&:nth-child(2n) td {
					background: transparent;
				}
				&:hover td {
					background: transparent;
				}
				td {
					padding: 30px;
					text-align: start;
					@media #{$md, $sm, $xs} {
						padding: 15px;
					}
					@media #{ $sm, $xs} {
						display: block;
						width: 100%;
						text-align: center;
						border: 0;
						border-bottom: 1px solid var(--tj-color-border-2);
					}
					&.woosw-item--info {
						padding-inline-start: 0;
					}
					&.woosw-item--image {
						padding-inline-end: 15px;
					}
					a.wc-forward,
					.tj-cart-btn {
						width: auto;
						display: inline-flex;
						text-decoration: none;
					}
					.woosw-item--stock,
					.woosw-item--atc {
						display: inline-block;
						@media #{ $sm, $xs} {
							display: block;
						}
					}
					.woosw-item--atc {
						float: right;
						margin-inline-start: 10px;
						@media #{ $sm, $xs} {
							float: unset;
							margin: 0;
							margin-top: 10px;
						}
					}
				}

				.woosw-item--name a {
					font-size: 20px;
					font-family: var(--tj-ff-heading);
					line-height: 1.2;
					letter-spacing: -0.025em;
					font-weight: 600 !important;
					text-decoration: none;
					@media #{$md, $sm, $xs} {
						font-size: 16px;
					}
				}
				.woosw-item--image {
					width: 150px;
					border-inline-end: 1px solid transparent;
					border-radius: 0;
					@media #{$md, $sm, $xs} {
						width: 100px;
					}
					@media #{ $sm, $xs} {
						width: 100%;
					}
					img {
						width: 100px;
						border-radius: 0;
					}
				}

				.woosw-item--remove span,
				.woosw-item--add span {
					display: inline-block;
					width: 36px;
					height: 36px;
					text-align: center;
					cursor: pointer;
					border: 1px solid var(--tj-color-border-2);
					display: inline-flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					color: var(--tj-color-heading-primary);
					&::before {
						color: var(--tj-color-heading-primary);
						font-size: 22px;
					}
					&:hover {
						border-color: var(--tj-color-theme-primary);
						background-color: var(--tj-color-theme-primary);
						color: var(--tj-color-common-white);
						&::before {
							color: var(--tj-color-common-white);
						}
					}
				}
			}
		}
	}
	.woosw-actions {
		display: none;
	}
}

/* product details */

.single-product {
	& .tj-shop-area {
		& .related.products {
			& h2 {
				margin-bottom: 25px;
			}
		}
	}
}

.tj-login-wrapper {
	& .tj-btn {
		background-color: var(--tj-color-heading-primary);
		&:hover {
			background-color: var(--tj-color-theme-primary);
		}
	}
}

/* order details css start */

.woocommerce-order {
	& .woocommerce-notice--success.woocommerce-thankyou-order-received {
		font-size: 24px;
		font-weight: 600;
		color: var(--tj-color-heading-primary);
		border: 2px dashed var(--tj-color-theme-primary);
		padding: 35px 30px;
		text-align: center;
		margin-bottom: 25px;
	}

	&
		.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
		padding-inline-start: 0;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding: 20px 20px 0;
		box-shadow: 1px 6px 19px rgba(3, 4, 28, 0.1);
		margin-bottom: 55px;

		@media #{$sm, $xs} {
			justify-content: start;
		}
		& li {
			list-style: none;
			display: inline-block;
			margin-bottom: 20px;
			margin-inline-end: 30px;
			& strong {
				display: block;
			}
		}
	}
}

.woocommerce-order-received {
	& .tj-page-area {
		background-color: transparent;
	}
}

.tj-order-details-wrapper {
	display: flex;

	@media #{$sm, $xs} {
		display: block;
	}
	& .woocommerce-order-details {
		flex: 50%;
		margin-inline-end: 50px;

		@media #{$sm, $xs} {
			flex: 100%;
			margin-inline-end: 0;
			margin-bottom: 50px;
		}

		& .woocommerce-order-details__title {
			margin-bottom: 15px;
			font-size: 22px;
		}
	}
	& .woocommerce-customer-details {
		flex: 50%;
		@media #{$sm, $xs} {
			flex: 100%;
		}
		& [class*="col-"] {
			width: inherit;
		}

		& .woocommerce-column__title {
			margin-bottom: 15px;
			font-size: 22px;
		}

		& .woocommerce-column--billing-address {
			margin-bottom: 35px;
		}
	}

	& .shop_table {
		width: 100%;

		& thead {
			& tr {
				& th {
					text-align: start;
					padding: 8px 12px;
					color: var(--tj-color-heading-primary);
					font-weight: 700;
					border-color: var(--tj-color-border-2);
				}
			}
		}

		& tbody {
			& tr {
				& td {
					padding: 8px 12px;
					border-color: var(--tj-color-border-2);
					&.product-name {
						text-align: start;
						& a {
							color: var(--tj-color-heading-primary);
						}
					}

					&.product-total {
						text-align: start;
					}
				}
			}
		}
		& tfoot {
			& tr {
				& th {
					padding: 8px 12px;
					text-align: start;
					font-weight: 700;
					color: var(--tj-color-heading-primary);
					border-color: var(--tj-color-border-2);
				}
				& td {
					border-color: var(--tj-color-border-2);
					padding: 8px 12px;
					text-align: start;
				}
			}
		}
	}
}

.tj-empty-cart {
	text-align: center;
	margin-bottom: 35px;
	& img {
		max-width: 450px;
	}
}

.cart-empty.woocommerce-info {
	font-family: var(--tj-ff-heading);
	font-size: 32px;
	font-weight: 600;
	line-height: 1.2;
	letter-spacing: -0.025em;
	margin-bottom: 35px;
	text-align: center;
}

/* my account css */

div.woocommerce {
	.woocommerce-MyAccount-navigation {
		& ul {
			padding-inline-start: 0;
			border-bottom: 1px solid var(--tj-color-border-2);
			& li {
				list-style: none;
				display: inline-block;
				margin-bottom: 0;
				margin-inline-end: 20px;

				&.is-active {
					& a {
						&::after {
							width: 100%;
							inset-inline-start: 0;
							inset-inline-end: auto;
						}
					}
				}
				& a {
					font-size: 16px;
					padding: 8px 0;
					display: inline-block;
					position: relative;
					text-decoration: none;
					&:hover {
						color: var(--tj-color-theme-primary);
					}

					&::after {
						position: absolute;
						content: "";
						inset-inline-start: auto;
						inset-inline-end: 0;
						height: 2px;
						width: 0;
						bottom: 0;
						background-color: var(--tj-color-theme-primary);
					}
				}
			}
		}
	}

	& .woocommerce-MyAccount-content {
		.woocommerce-info a.wc-forward {
			width: max-content;
			margin: unset;
			margin-top: 10px;
			text-decoration: none;
		}
		& > p {
			& a {
				color: var(--tj-color-theme-primary);
			}
		}
		& .my_account_orders {
			width: 100%;
		}

		& .woocommerce-order-downloads {
			& .shop_table {
				width: 100%;
			}
		}

		& .woocommerce-Addresses {
			&.u-columns {
				display: flex;
				flex-wrap: wrap;
				margin-top: 15px;
			}
			& .woocommerce-Address {
				width: 50%;

				.woocommerce-Address-title {
					display: flex;
					h3 {
						font-size: 28px;
						line-height: 1;
						font-weight: 500;
					}
					& > a {
						color: var(--tj-color-theme-primary);
						font-weight: 500;
						font-size: 16px;
						margin-inline-start: 40px;
					}
				}
			}
		}

		& .woocommerce-MyAccount-orders {
			& th {
				font-weight: 700;
			}
		}

		& .edit-account {
			& fieldset {
				margin-top: 35px;
			}
			& legend {
				font-size: 22px;
				color: var(--tj-color-heading-primary);
				font-weight: 500;
			}

			& label {
				color: var(--tj-color-heading-primary);
			}

			& input {
				height: 46px;
				line-height: 46px;
			}

			& .tj-btn {
				&:hover {
					background-color: var(--tj-color-heading-primary);
					color: var(--tj-color-common-white);
				}
			}
		}

		& .woocommerce-address-fields__field-wrapper {
			& input {
				height: 46px;
				line-height: 46px;
			}
		}

		& .tj-btn {
			&:hover {
				background-color: var(--tj-color-heading-primary);
			}
		}
	}
}

/* tp progress bar */

.tj-free-progress-bar {
	padding: 20px 30px 30px;
	border: 1px solid #e7e8eb;
	width: 100%;
	margin-bottom: 30px;

	& .free-shipping-notice {
		color: var(--tj-color-heading-primary);
		font-size: 13px;
		margin-bottom: 5px;
		font-weight: 500;
		& a {
			color: var(--tj-color-theme-primary);
		}

		& span {
			color: var(--tj-color-theme-primary);
			font-weight: 700;
		}
	}
	.tj-progress-bar {
		height: 6px;
		background-color: #f2f3f5;
		border-radius: 10px;
		overflow: hidden;

		& .progress {
			background-color: var(--tj-color-theme-primary);
			height: 6px;
		}
	}

	& .progress-bar-striped {
		background-image: linear-gradient(
			45deg,
			rgba(255, 255, 255, 1) 25%,
			transparent 25%,
			transparent 50%,
			rgba(255, 255, 255, 0.5) 50%,
			rgba(255, 255, 255, 0.5) 75%,
			transparent 75%,
			transparent
		);
	}
}

.cartmini__widget {
	.woocommerce-mini-cart__total {
		padding: 20px;
		margin-bottom: 0;

		& span {
			float: right;
		}
	}

	& .product_list_widget {
		max-height: 700px;
		overflow-y: scroll;

		overscroll-behavior-y: contain;
		scrollbar-width: thin;
		scrollbar-color: rgba($color: #f50963, $alpha: 0.5) #fff;
		&::-webkit-scrollbar {
			display: thin; /* for Chrome, Safari, and Opera */
		}
	}

	& .woocommerce-mini-cart__buttons {
		padding: 20px;
		& .button {
			display: inline-block;
			font-weight: 500;
			color: var(--tj-color-heading-primary);
			font-family: var(--tj-ff-body);
			padding: 10px 30px;
			font-size: 16px;
			text-transform: capitalize;
			border: 1px solid #e7e8eb;
			text-align: center;
			width: 100%;

			&:hover {
				background-color: var(--tj-color-heading-primary);
				color: var(--tj-color-common-white);
			}
		}
		& .button:not(.checkout) {
			background-color: var(--tj-color-theme-bg);
			color: var(--tj-color-heading-primary);
			margin-bottom: 15px;

			&:hover {
				color: var(--tj-color-common-white);
				background-color: var(--tj-color-heading-primary);
				border-color: var(--tj-color-heading-primary);
			}
		}
	}
}

.woosq-open .select2-container {
	z-index: 9;
}

/* product sidebar */
div.product-widget {
	padding: 35px 30px;
	border: 1px solid var(--tj-color-border-2);
	&:not(:last-child) {
		margin-bottom: 30px;
	}
	@media #{$sm, $xs} {
		padding: 20px;
	}
	.product-widget-title {
		margin-bottom: 30px;
		position: relative;
		padding-bottom: 12px;
		font-size: 20px;
		&::before,
		&::after {
			position: absolute;
			content: "";
			bottom: 0;
			height: 3px;
			background: var(--tj-color-theme-primary);
		}
		&::before {
			inset-inline-start: 0;
			width: 40px;
		}
		&::after {
			inset-inline-start: 45px;
			width: 10px;
		}
	}

	& .select2-container {
		margin-bottom: 20px;
		& .select2-selection--single {
			height: 40px;
			line-height: 38px;
			border: 1px solid #e7e8eb;
			background-color: var(--tj-color-common-white);
			border-radius: 0;

			&:focus {
				background-color: var(--tj-color-common-white);
				.select2-selection--single {
					background-color: var(--tj-color-common-white);
					border-color: var(--tj-color-theme-primary);
				}
			}
		}

		& .select2-selection__rendered {
			line-height: 40px;
			padding-inline-start: 20px;
			padding-inline-end: 20px;
		}

		& .select2-selection__arrow {
			height: 40px;
			inset-inline-end: 20px;
		}
	}
	.select2-container--default
		.select2-selection--single
		.select2-selection__clear {
		cursor: pointer;
		float: right;
		font-weight: 700;
		padding-inline-end: 4px;
		z-index: 2;
		background-color: var(--tj-color-common-white);
		top: -2px;
	}

	/* rating filter */
	&.widget_rating_filter {
		& ul {
			& li {
				list-style: none;
			}
		}
	}

	/* product filter by color list*/
	.woocommerce-widget-layered-nav-list {
		& li {
			list-style: none;
			font-size: 14px;
			color: #998f8f;
			margin-bottom: 5px;
			& span {
				font-weight: 400;
			}

			& a {
				position: relative;
				padding-inline-start: 15px;
				color: var(--tj-color-text-body);
				&:hover {
					color: var(--tj-color-theme-primary);
				}
				&::after {
					position: absolute;
					content: "";
					inset-inline-start: 0;
					top: 53%;
					height: 4px;
					width: 4px;
					background-color: var(--tj-color-text-body);
					border-radius: 50%;
					@include transform(translateY(-50%));
				}

				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}
		}
	}

	/* filter categories */
	&.widget_product_categories {
		ul {
			margin: 0;
			padding: 0;
			list-style: none;
			li {
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;
				font-size: 16px;
				font-family: var(--tj-ff-heading);
				font-weight: var(--tj-fw-sbold);
				padding: 15px 24px;
				margin-bottom: 20px;
				color: var(--tj-color-heading-primary);
				background: var(--tj-color-theme-bg);
				z-index: 1;
				@include transition(all 0.4s ease-in-out 0s);
				&:last-child {
					margin-bottom: 0;
				}
				a {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: var(--tj-color-heading-primary);
				}
				&:hover {
					color: var(--tj-color-common-white);
					background-color: var(--tj-color-theme-primary);
					a {
						color: var(--tj-color-common-white);
					}
				}
			}
		}
		@media #{$lg, $md, $sm, $xs} {
			ul {
				li {
					padding: 15px;
				}
			}
		}
	}

	&.widget_top_rated_products,
	&.widget_recent_reviews,
	&.widget_products {
		& ul {
			& li {
				list-style: none;
				&:not(:last-child) {
					margin-bottom: 20px;
				}
			}
		}
	}

	& .tj-product-sidebar {
		&-rating {
			&-thumb {
				& img {
					max-width: inherit;
					width: 90px;
				}
			}
			&-title {
				font-size: 16px;
				& a {
					&:hover {
						color: var(--tj-color-theme-primary);
					}
				}
			}
			&-price {
				display: inline-flex;
				gap: 8px;
				flex-direction: row-reverse;
				span {
					font-family: var(--tj-ff-body);
					font-weight: 500;
					font-size: 16px;
					color: var(--tj-color-text-body);
				}
				del,
				ins {
					text-decoration: none;
				}
				del {
					color: var();
					.woocommerce-Price-amount,
					span {
						text-decoration-line: line-through;
						color: var(--tj-color-common-black-2);
					}
				}
			}
			&-content {
				.star-rating {
					display: block;
				}

				& .reviewer {
					& span {
						font-weight: 400;
						& span {
							font-weight: 500;
						}
					}
				}
			}
		}
	}

	&.widget_product_tag_cloud {
		.tagcloud a {
			font-size: 16px !important;
			border: 1px solid var(--tj-color-theme-bg);
			box-shadow: none;
			background-color: var(--tj-color-theme-bg);
			padding: 5px 10px;
			&:hover {
				background-color: var(--tj-color-theme-primary);
				border-color: var(--tj-color-theme-primary);
				color: var(--tj-color-common-white);
			}
		}
	}
}
/* price range slider */
.tj-shop-sidebar {
	.price_slider_wrapper {
		padding: 30px 20px;
		background-color: var(--tj-color-theme-bg);
	}
	.ui-widget-content {
		position: relative;
		height: 4px;
		background-color: var(--tj-color-border-2);
		margin-bottom: 30px;

		.ui-slider-range {
			position: absolute;
			display: block;
			width: 100%;
			height: 4px;
			border: 0;
			background-color: var(--tj-color-theme-dark);
			border-radius: 8px;
			z-index: 1;
		}

		.ui-slider-handle {
			inset-inline-start: 0%;
			position: absolute;
			z-index: 2;
			outline: 0;
			cursor: pointer;
			background-color: var(--tj-color-common-white);
			border-radius: 100%;
			border: 2px solid var(--tj-color-theme-dark);
			height: 14px;
			top: -5px;
			width: 14px;
			margin: 0;
			-webkit-box-shadow: none;
			box-shadow: none;
			-webkit-transform: translateX(0px);
			transform: translateX(0px);
			transition: unset;

			&:last-child {
				inset-inline-start: 100%;
				-webkit-transform: translateX(-100%);
				transform: translateX(-100%);
			}
		}
	}

	.price_slider_amount {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;

		.button {
			-webkit-box-ordinal-group: 3;
			-ms-flex-order: 2;
			order: 2;
			font-size: 16px;
			font-weight: 700;
			height: auto;
			margin-inline-start: auto;
			padding: 8px 23px;
			border-radius: 30px;
			background-color: var(--tj-color-theme-primary);
			color: var(--tj-color-common-white);
			&:hover {
				background-color: var(--tj-color-theme-dark);
			}
		}

		.price_label {
			font-size: 16px;
			line-height: 1;
			padding: 12px 15px;
			background-color: var(--tj-color-common-white);
			border: 1px solid var(--tj-color-border-2);
			span {
				font-weight: 500;
				color: var(--tj-color-heading-primary);
			}
		}
	}
}

.product-widget.woocommerce {
	&.widget_recent_reviews {
		ul {
			li {
				list-style: none;
			}
		}
	}
}
/* product sidebar css end */

.woocommerce-ordering {
	& .nice-select {
		ul.list {
			width: auto;
			inset-inline-start: auto;
			inset-inline-end: 0;
		}
	}
}

.tj-product-details-price {
	& del {
		& .woocommerce-Price-amount,
		& .woocommerce-Price-currencySymbol {
			& bdi {
				margin-inline-end: 7px;
			}
		}
	}
	& .price {
		& ins {
			text-decoration: none;
		}
	}
}

.woosc-quick-table-products {
	& .woosc_table {
		& td {
			& img {
				width: 100px;
			}
		}
		& .star-rating {
			display: inline-block;
		}
		& a {
			&.added_to_cart {
				&.wc-forward {
					background: var(--tj-color-theme-primary);
					padding: 5px 20px;
					color: #fff;
				}
			}
			&.ajax_add_to_cart {
				&.added {
					display: none !important;
				}
			}
		}
	}
}

.woocommerce-product-gallery {
	& ol {
		&.product-thumbnails {
			padding-inline-start: 0;
			margin-top: 20px;
		}
	}
}

.outofstock {
	& .woocommerce-grouped-product-list-item__quantity {
		& .cart-button {
			display: none;
		}
	}
}

.stock {
	&.out-of-stock {
		color: var(--tj-color-theme-primary);
		display: inline-block;
		margin-bottom: 5px;
	}
}
.tj-product-area {
	.nice-select.open {
		& .list {
			width: auto;
		}
	}
}

@media (max-width: 575px) {
	.woocommerce-ordering {
		& .nice-select {
			ul.list {
				inset-inline-start: auto;
			}
		}
	}
}

.tj-product-details__list-img {
	& .tj-product__thumb-topsall {
		inset-inline-start: 25px;
		inset-inline-end: auto;
		position: absolute;
		top: 25px;
		padding: 10px 12px 10px;
		background-color: var(--tj-color-theme-primary);
		color: var(--tj-color-text-body);
		font-size: 13px;
		line-height: 1;
		z-index: 2;
	}
}

/*mini cart */

.tj-mini-card {
	display: inline-block;
	position: relative;
	cursor: pointer;
	& div.mini_shopping_cart_box {
		position: absolute;
		top: 110%;
		inset-inline-end: 0;
		width: 300px;
		background: #fff;
		padding: 1.25rem;
		background-color: #fff;
		-webkit-box-shadow: 0px 5px 10px rgba(62, 68, 90, 0.1);
		box-shadow: 0px 5px 10px rgba(62, 68, 90, 0.1);
		border: 1px solid #edeef5;
		border-radius: 10px;
		transition: 0.3s;
		opacity: 0 !important;
		visibility: hidden;
		z-index: 10;
		text-align: start;
		@media (max-width: 767px) {
			display: none;
		}
		& .cartmini__empty {
			& .tj-btn {
				margin-inline-start: 0;
				font-size: 14px;
			}
		}
		& .mini_cart_item {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-webkit-box-align: center;
			-ms-flex-align: center;
			align-items: center;
			-webkit-box-orient: horizontal;
			-webkit-box-direction: normal;
			-ms-flex-flow: row wrap;
			flex-flow: row wrap;
			border-bottom: 1px solid #edeef5;
			padding-bottom: 10px;
			margin-bottom: 10px;
			& .cartmini__thumb {
				& a {
					margin-inline-start: 0;
					& img {
						width: 60px;
					}
				}
			}
			& .mini-cart-remove {
				& a {
					&:hover {
						color: var(--tj-color-theme-primary);
					}
				}
			}
			& .cartmini__content {
				position: static;
				-webkit-box-flex: 1;
				-ms-flex: 1;
				flex: 1;
				padding: 0;
				& h5 {
					margin-bottom: 0;
				}
				& a {
					font-size: 14px;
					margin-inline-start: 10px;
					font-weight: 600;
					display: inline-block;
					&:hover {
						color: var(--tj-color-theme-primary);
					}
				}
				& .cartmini__price-wrapper {
					margin-inline-start: 10px;
					& .quantity {
						display: inline-block;
						margin-inline-end: 0;
						margin-bottom: 0;
						font-size: 14px;
						& .woocommerce-Price-amount {
							&.amount {
								color: var(--tj-color-theme-primary);
							}
						}
					}
				}
			}
		}
		& .woocommerce-mini-cart__total {
			margin-top: 20px;
			& > strong {
				font-size: 16px;
				font-weight: 600;
				color: #c2c2d3;
			}
			& .woocommerce-Price-amount {
				float: right;
				& bdi {
					font-weight: 600;
					color: var(--tj-color-theme-primary);
				}
			}
		}
		& .woocommerce-mini-cart__buttons {
			& .wc-forward {
				float: none;
				color: #222;
				display: block;
				border: 1px solid #ddd;
				text-align: center;
				padding: 7px 15px;
				margin-top: 10px;
				background: none;
				margin-inline-start: 0;
				font-size: 20px;
				&.checkout {
					background: var(--tj-color-theme-primary);
					border-color: var(--tj-color-theme-primary);
					color: #fff;
				}
			}
		}
	}
}

.tj-mini-card:hover .mini_shopping_cart_box {
	opacity: 1 !important;
	visibility: visible;
	top: 105%;
}

#woosq-popup {
	& .variations {
		& select {
			-webkit-tap-highlight-color: transparent;
			background-color: #fff;
			border-radius: 5px;
			border: solid 1px #e8e8e8;
			box-sizing: border-box;
			clear: both;
			cursor: pointer;
			display: block;
			float: left;
			font-family: inherit;
			font-size: 14px;
			font-weight: normal;
			height: 42px;
			line-height: 40px;
			outline: none;
			padding-inline-start: 18px;
			padding-inline-end: 30px;
			position: relative;
			text-align: left !important;
			-webkit-transition: all 0.2s ease-in-out;
			transition: all 0.2s ease-in-out;
			-webkit-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			user-select: none;
			white-space: nowrap;
			width: auto;
		}
	}
	& .star-rating {
		display: inline-block;
	}
}

#review_form {
	& .comment-input {
		& label {
			font-size: 16px;
			color: var(--tj-color-text-body);
			font-weight: 500;
			margin-bottom: 10px;
		}
	}

	& .comment-respond {
		& > h3 {
			line-height: 1;
			margin-bottom: 20px;
			margin-top: 25px;
		}
		button {
			margin-top: 25px;
		}
	}
}

.woocommerce-cart-form {
	& .tj-product-details__quantity {
		padding: 10px 15px;
	}
	& input {
		width: 100px;
	}
}

// my account form
#customer_login {
	display: flex;
	@media (max-width: 768px) {
		display: block;
	}
	& .u-column {
		&1,
		&2 {
			width: 50%;
			display: inline-block;
			padding: 20px;
			@media (max-width: 768px) {
				width: 100%;
			}
			& h2 {
				font-size: 28px;
				font-weight: 600;
				color: var(--tj-color-heading-primary);
				margin-bottom: 15px;
			}
			& label {
				margin-inline-start: 0;
				font-size: 16px;
				margin-bottom: 10px;
				&.woocommerce-form__label-for-checkbox {
					display: flex;
					align-items: center;
					& span {
						font-size: 16px;
						cursor: pointer;
					}
				}
			}
			& input {
				border-radius: 6px;
				padding: 0px 20px;
				font-size: 14px;
				width: 100%;
				height: 55px;
				border: 0;
				outline: 0;
				font-weight: 500;
				font-size: 16px;
				color: #87888a;
				box-shadow: inset 0 0 0 1px #e5e5e5;
				transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
				color: var(--tj-color-heading-primary);

				&[type="checkbox"] {
					width: 15px;
					margin-inline-end: 8px;
					cursor: pointer;
				}

				&:focus {
					box-shadow: 0px 1px 2px 1px rgba(32, 33, 36, 0.06),
						inset 0 0 0 2px var(--tj-color-theme-primary);
				}
			}
			& .woocommerce-form-login__submit,
			& .woocommerce-form-register__submit {
				font-family: var(--tj-ff-body);
				font-weight: 600;
				font-size: 16px;
				line-height: 1;
				color: var(--tj-color-common-white);
				background-color: var(--tj-color-theme-primary);
				display: inline-block;
				padding: 12px 32px;
				border-radius: 4px;
				border: 2px solid var(--tj-color-theme-primary);
				&:hover {
					color: var(--tj-color-theme-primary);
					background-color: transparent;
				}
			}
			& .lost_password {
				font-size: 16px;
				color: #6f7172;
				text-decoration: underline;
				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}
		}
	}
}

.woocommerce-ResetPassword {
	& label {
		margin-inline-start: 0;
		font-size: 16px;
		margin-bottom: 10px;
		&.woocommerce-form__label-for-checkbox {
			display: flex;
			align-items: center;
			& span {
				font-size: 16px;
				cursor: pointer;
			}
		}
	}
	& input {
		border-radius: 0;
		padding: 0px 20px;
		font-size: 14px;
		width: 100%;
		height: 55px;
		border: 0;
		outline: 0;
		font-weight: 500;
		font-size: 16px;
		color: #87888a;
		box-shadow: inset 0 0 0 1px #e5e5e5;
		transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
		color: var(--tj-color-heading-primary);

		&[type="checkbox"] {
			width: 15px;
			margin-inline-end: 8px;
			cursor: pointer;
		}

		&:focus {
			box-shadow: 0px 1px 1px 1px rgba(32, 33, 36, 0.06),
				inset 0 0 0 1px #4260ff;
		}
	}
	& .woocommerce-Button {
		height: 60px;
		padding: 0 50px;
		margin-top: 20px;
		font-weight: 600;
		font-size: 18px;
		line-height: 24px;
		text-transform: capitalize;
		color: var(--tj-color-common-white);
		display: inline-flex;
		gap: 6px;
		background: var(--tj-color-theme-primary);
		justify-content: center;
		align-items: center;
		-webkit-transition: all 0.4s ease-out 0s;
		-moz-transition: all 0.4s ease-out 0s;
		-ms-transition: all 0.4s ease-out 0s;
		-o-transition: all 0.4s ease-out 0s;
		transition: all 0.4s ease-out 0s;
		border-radius: 60px;
		&:focus,
		&:hover {
			color: var(--tj-color-common-white);
			background: var(--tj-color-theme-dark);
		}
	}
}

.woocommerce-notices-wrapper {
	& .woocommerce-error {
		padding: 0;
		list-style: none;
		& li {
			background: var(--tj-color-theme-bg);
			padding: 15px 20px;
			margin-bottom: 30px;
			border: 1px solid var(--tj-color-theme-bg);
			color: var(--tj-color-text-body);
		}
	}
}

.woocommerce {
	&:not(:has(#customer_login)) {
		.woo-login-form {
			max-width: 645px;
			margin: 0 auto;
		}
	}
	.woo-login-form {
		h3 {
			margin-bottom: 20px;
		}
	}
	.woo-lost-password {
		max-width: 645px;
		margin: 0 auto;
	}
}
.woocommerce-form-register,
.woocommerce-form-login {
	& label {
		margin-inline-start: 0;
		margin-bottom: 5px;
		font-size: 16px;
		&.woocommerce-form__label-for-checkbox {
			display: flex;
			align-items: center;
			& span {
				font-size: 16px;
			}
		}
	}
	& input {
		background: transparent !important;
		border: 1px solid var(--tj-color-theme-dark) !important;
		border-radius: 0;
		height: 48px !important;
		padding: 0 0 0 10px;
		width: 100%;
		outline: none;
		-webkit-box-shadow: none;
		-moz-box-shadow: none;
		-ms-box-shadow: none;
		-o-box-shadow: none;
		box-shadow: none;
		display: block;
		&[type="checkbox"] {
			width: 15px;
			margin-inline-end: 8px;
		}
		&:focus {
			background: var(--tj-color-theme-bg) !important;
		}
	}
	.show-password-input {
		display: none;
	}
	& .woocommerce-button {
		width: 100%;
		display: block;
		text-align: center;
		font-size: 16px;
		font-weight: 500;
		padding: 12px 30px;
		border-radius: 0;
		background-color: var(--tj-color-theme-primary);
		color: var(--tj-color-common-white);
		.btn-text {
			display: inline-flex;
			overflow: hidden;
			color: inherit;
			text-shadow: 0 23px 0 currentColor;
			span {
				backface-visibility: hidden;
				transform: translateY(0);
				transition: 0.5s;
			}
		}
		&:hover {
			background-color: var(--tj-color-theme-dark);
			color: var(--tj-color-common-white);
			.btn-text {
				span {
					transform: translateY(-24px);
				}
			}
		}
	}
}
.woocommerce-address-fields {
	.nice-select {
		display: block;
		width: 100%;
		border-radius: 0;
	}
}
#shipping_address_1 {
	margin-bottom: 15px;
}
.woocommerce-address-fields,
.edit-account {
	& label {
		margin-inline-start: 0;
		font-size: 14px;
		margin-bottom: 10px;
		&.woocommerce-form__label-for-checkbox {
			display: flex;
			align-items: center;
			& span {
				font-size: 16px;
				cursor: pointer;
			}
		}
	}
	& input {
		border-radius: 0;
		padding: 0px 20px;
		font-size: 14px;
		width: 100%;
		height: 55px;
		border: 0;
		outline: 0;
		font-weight: 500;
		font-size: 16px;
		color: #87888a;
		box-shadow: inset 0 0 0 1px #e5e5e5;
		transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
		color: var(--tj-color-heading-primary);

		&[type="checkbox"] {
			width: 15px;
			margin-inline-end: 8px;
			cursor: pointer;
		}

		&:focus {
			box-shadow: 0px 1px 1px 1px rgba(32, 33, 36, 0.06),
				inset 0 0 0 1px #4260ff;
		}
	}
	button[name="save_address"],
	& .woocommerce-Button {
		height: 60px;
		padding: 0 50px;
		margin-top: 20px;
		font-weight: 600;
		font-size: 18px;
		line-height: 24px;
		text-transform: capitalize;
		color: var(--tj-color-common-white);
		display: inline-flex;
		gap: 6px;
		background: var(--tj-color-theme-primary);
		justify-content: center;
		align-items: center;
		-webkit-transition: all 0.4s ease-out 0s;
		-moz-transition: all 0.4s ease-out 0s;
		-ms-transition: all 0.4s ease-out 0s;
		-o-transition: all 0.4s ease-out 0s;
		transition: all 0.4s ease-out 0s;
		border-radius: 60px;
		&:focus,
		&:hover {
			color: var(--tj-color-common-white);
			background: var(--tj-color-theme-dark);
		}
	}
}

.woosw-copy-label {
	color: #6f7172;
}
#woosw_copy_btn {
	padding: 3px 20px;
	background: var(--tj-color-theme-primary);
	color: #fff;
	font-weight: 500;
	border: none;
	margin-inline-start: 10px;
}

/*rating*/
.star-rating {
	position: relative;
	font-size: 12px;
	overflow: hidden;
	line-height: 1;
	display: inline-block;
}
.star-rating::before {
	font-family: "Font Awesome 5 Pro";
	content: "\f005\f005\f005\f005\f005";
	text-transform: uppercase;
	letter-spacing: 3px;
	color: var(--tj-color-theme-primary);
	font-size: 12px;
	line-height: 1;
}
.star-rating span {
	position: absolute;
	top: 0;
	inset-inline-start: 0;
	overflow: hidden;
	font-size: 0;
	width: max-content !important;
}
.star-rating span::before {
	font-family: "Font Awesome 5 Pro";
	content: "\f005\f005\f005\f005\f005";
	text-transform: uppercase;
	letter-spacing: 3px;
	color: var(--tj-color-theme-primary);
	font-weight: 900;
	display: inline-block;
	font-size: 12px;
	line-height: 1;
}
/*rating end*/
a.ajax_add_to_cart.added {
	display: none !important;
}

.woocommerce p.stars a {
	position: relative;
	height: 1em;
	width: 1em;
	text-indent: -999em;
	display: inline-block;
	text-decoration: none;
	color: var(--tj-color-theme-primary);
}
.woocommerce p.stars a::before {
	display: block;
	position: absolute;
	top: 0;
	inset-inline-start: 0;
	width: 1em;
	height: 1em;
	line-height: 1;
	font-family: var(--tj-ff-fontawesome);
	content: "\f005";
	text-indent: 0;
}
.woocommerce p.stars a:hover ~ a::before {
	content: "\f005";
	font-weight: 400;
}

.woocommerce p.stars:hover a::before {
	content: "\f005";
	font-weight: 700;
}

.woocommerce p.stars.selected a.active::before {
	content: "\f005";
	font-weight: 700;
}
.woocommerce p.stars.selected a.active ~ a::before {
	content: "\f005";
	font-weight: 400;
}
.woocommerce p.stars.selected a:not(.active)::before {
	content: "\f005";
	font-weight: 700;
}
/*spinner */
@-webkit-keyframes spin {
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}
@keyframes spin {
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

.woocommerce a.button.loading::before,
.woocommerce button.button.loading::after,
.woocommerce input.button.loading::after {
	font-family: var(--tj-ff-fontawesome);
	content: "\f110";
	vertical-align: top;
	font-weight: 400;
	top: 0.618em;
	font-size: 14px;
	inset-inline-end: 1em;
	-webkit-animation: spin 2s linear infinite;
	animation: spin 2s linear infinite;
	color: var(--tj-color-heading-primary);
	display: inline-block;
	transition: all 0.3s ease-out 0s;
}

.tj-product-action .added_to_cart.wc-forward {
	font-size: 0;
}
.loading svg {
	display: none;
}

/*compare*/
.woosc-area {
	background: no-repeat !important;
}

/* archive */
.tj-product:hover {
	position: relative;
	z-index: 2;
}

section.woosc-quick-table > h2 {
	font-size: 26px;
	color: var(--tj-color-text-body);
	font-weight: 600;
}

a.cart-button.icon-btn.button.stock-out {
	cursor: not-allowed;
}

.grouped_form {
	& a {
		color: var(--tj-color-text-body);
		&:hover {
			color: var(--tj-color-theme-primary);
		}
	}
	& .variations {
		& .nice-select {
			float: none;
		}
	}
	& .tj-product-details__quantity {
		padding: 5px 13px;
		margin-inline-end: 5px;
		margin-top: 0;
		margin-bottom: 0;
		& .tj-cart-minus,
		& .tj-cart-plus {
			font-size: 14px;
		}
		& input {
			font-size: 14px;
		}
	}
	& del {
		font-size: 16px;
		color: var(--tj-color-theme-bg);
		font-weight: 600;
		& .woocommerce-Price-amount {
			font-size: 16px;
			color: var(--tj-color-theme-bg);
			font-weight: 500;
		}
	}
	& ins {
		text-decoration: none;
		& .woocommerce-Price-amount {
			color: var(--tj-color-text-body);
			font-weight: 600;
			font-size: 16px;
		}
	}
	& .woocommerce-Price-amount {
		color: var(--tj-color-text-body);
		font-weight: 600;
		font-size: 16px;
	}
}

.woocommerce-shop {
	.woocommerce-notices-wrapper {
		display: none;
	}
	.tj-product__thumb-topsall {
		position: absolute;
		inset-inline-end: 25px;
		top: 15px;
		span {
			background-color: var(--tj-color-theme-primary);
			color: var(--tj-color-text-body);
			padding: 10px 12px 10px;
		}
	}
	.tj-shop-list-title {
		font-size: 16px;
		color: var(--tj-color-text-body);
		margin-bottom: 0;
	}
}
.tj-shop-listing {
	gap: 20px;
	.woocommerce-notices-wrapper {
		display: none;
	}
}
.tj-shop-listing-popup {
	.nice-select {
		background: transparent;
		padding: 0 15px;
		border-radius: 0;
		width: 220px;
		@media #{$xs} {
			width: auto;
		}
		&::after {
			border: 0;
			content: "\e91a";
			font-family: "solvior-icons" !important;
			height: auto;
			width: auto;
			transform: unset;
			transform-origin: unset;
			top: 6px;
		}
		.list {
			border-radius: 0;
		}
	}
	& .orderby {
		& .current {
			margin-inline-end: 25px;
		}
	}
}

a.added_to_cart.wc-forward::before {
	content: "\f00c";
	font-family: var(--tj-ff-fontawesome);
	position: relative;
	font-size: 16px;
	font-weight: 400;
}

.tj-product-price {
	.woocommerce-Price-amount {
		font-weight: 400;
		font-size: 16px;
		color: var(--tj-color-heading-primary);
	}
	ins {
		text-decoration: none;
	}
	del {
		.woocommerce-Price-amount {
			font-weight: 400;
			font-size: 16px;
			color: var(--tj-color-text-body);
		}
	}
}

.tj-product-details__cart {
	& .product-add-cart-btn {
		margin-inline-end: 5px;

		& svg {
			@include transform(translateY(-2px));
			margin-inline-end: 3px;
		}
	}
	& .product-action-btn {
		& .woosw-btn {
			font-size: 0;
			width: 48px;
			height: 48px;
			line-height: 47px;
			text-align: center;
			color: var(--tj-color-heading-primary);
			border: 1px solid #dadce0;
			border-radius: 4px;
			&:hover {
				color: var(--tj-color-common-white);
				background-color: var(--tj-color-theme-primary);
				border-color: var(--tj-color-theme-primary);
			}
			&::before {
				font-size: 16px;
				content: "\f004";
				font-family: var(--tj-ff-fontawesome);
				margin: 0;
			}
			&.woosw-btn-added {
				&::before {
					font-weight: 700;
				}
			}
		}
		& .woosc-btn {
			font-size: 0;
			width: 48px;
			height: 48px;
			line-height: 47px;
			text-align: center;
			color: var(--tj-color-heading-primary);
			border: 1px solid #dadce0;
			border-radius: 4px;
			&:hover {
				color: var(--tj-color-common-white);
				background-color: var(--tj-color-theme-primary);
				border-color: var(--tj-color-theme-primary);
			}
			&::before {
				font-size: 16px;
				content: "\f0ec";
				font-family: var(--tj-ff-fontawesome);
				margin: 0;
			}
			&.woosc-btn-added {
				&::before {
					font-size: 16px;
					content: "\f00c";
					font-family: var(--tj-ff-fontawesome);
					margin: 0;
					font-weight: 700;
				}
			}
		}
	}
}

#review_form_wrapper {
	& .stars {
		margin-top: 5px;
		margin-bottom: 0;
	}
}

.related-products {
	.woocommerce-notices-wrapper {
		display: none;
	}
	.tj-product__thumb-topsall {
		position: absolute;
		inset-inline-end: 25px;
		top: 15px;
		span {
			background-color: var(--tj-color-theme-primary);
			color: var(--tj-color-text-body);
			padding: 10px 12px 10px;
		}
	}
}

.flex-control-thumbs.product-thumbnails {
	& .slick-list {
		margin: 0 -6px;
	}
	& .slick-slide {
		margin: 0 6px;
	}

	& .slick-slide {
		display: -webkit-inline-box;
		display: -ms-inline-flexbox;
		display: inline-flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		cursor: pointer;
		border: 1px solid transparent;
		border-radius: 0;
		-webkit-transition: border-color 0.3s cubic-bezier(0.28, 0.12, 0.22, 1);
		transition: border-color 0.3s cubic-bezier(0.28, 0.12, 0.22, 1);

		&.slick-active {
			& img {
				&.flex-active {
					border: 1px solid var(--tj-color-theme-primary);
				}
			}
		}
		&:hover {
			cursor: pointer;
		}
	}
}
.product-thumbnails {
	padding-inline-start: 0;
	margin-top: 15px;
}

.cart-wrapper {
	& .cart_totals {
		& table {
			& tr,
			& tbody {
				border: none;
			}
		}
	}

	& .wc_payment_methods {
		padding-inline-start: 0;
		list-style: none;
		& .woocommerce-notice {
			list-style: none;
			color: var(--tj-color-theme-primary);
		}
	}

	& #order_review_heading {
		margin-bottom: 20px;
	}

	& .order-review-wrapper {
		& table {
			& tbody,
			& td,
			& tfoot,
			& th,
			& thead,
			& tr {
				border: none;
			}
		}
		& table {
			width: 100%;
			border: 0;
			& thead {
				& th {
					border: none;
					border-bottom: 1px solid var(--tj-color-border-2);
					padding-inline-start: 30px;
					padding-bottom: 0.75rem;
					text-align: start;
					font-family: var(--tj-ff-heading);
					font-size: 16px;
					font-weight: 600;
					color: var(--tj-color-heading-primary);
					&:last-child {
						text-align: start;
						padding-inline-end: 30px;
					}
				}
			}
			& tbody {
				& tr {
					border-bottom: 1px solid var(--tj-color-border-2);
					&.cart_item {
						& td {
							font-size: 16px;
							padding-inline-start: 30px;
							&:first-child {
								border-top: medium none;
								color: var(--tj-color-text-body);
								font-weight: normal;
								text-align: start;
								vertical-align: middle;
								width: 50%;
							}
							&:last-child {
								padding-inline-end: 30px;
								text-align: start;
								color: var(--tj-color-text-body);
							}

							& strong {
								color: var(--tj-color-text-body);
							}
						}
					}
					& td {
						border: 0;
						padding: 15px 0;
					}
				}
			}

			& tfoot {
				& tr {
					border-bottom: 1px solid var(--tj-color-border-2);
					&.woocommerce-shipping-totals.shipping {
						& th {
							border: none;
							border-bottom: 1px solid var(--tj-color-border-2);
							padding-inline-start: 0;
							color: var(--tj-color-heading-primary);
							padding: 15px 0;
							padding-inline-start: 30px;
							text-align: start;
							font-size: 14px;
							font-weight: 600;
						}
						& td {
							text-align: start;
							padding: 15px 0;
							padding-inline-start: 30px;
						}
					}

					&.cart-subtotal {
						& th {
							border: none;
							border-bottom: 1px solid var(--tj-color-border-2);
							color: var(--tj-color-heading-primary);
							text-align: start;
							font-size: 14px;
							font-weight: 600;
						}
						& td {
							border: none;
							border-bottom: 1px solid var(--tj-color-border-2);
							color: var(--tj-color-heading-primary);
							text-align: start;
							font-size: 14px;
						}
					}

					&.order-total {
						& th {
							text-align: start;
							font-weight: 600;
							color: var(--tj-color-heading-primary);
						}
						& td {
							text-align: start;
							span {
								bdi {
									color: var(--tj-color-heading-primary);
									font-weight: 600;
								}
							}
						}
					}
					& td,
					& th {
						border: 0;
						padding: 15px 0;
						padding-inline-start: 30px;
					}
				}
			}
		}
	}
}

.tj-checkout-billing {
	&-wrapper {
		background-color: var(--tj-color-common-white);
		& label {
			position: static;
			border: 0;
			margin-bottom: 7px;
			display: block;
			width: auto;
			color: var(--tj-color-heading-primary);
			& abbr {
				&.required {
					color: red;
					text-decoration: none;
				}
			}
			& span {
				font-size: 16px;
			}
		}

		& .woocommerce-input-wrapper {
			& span {
				display: block;
			}
		}

		& input {
			&[type="text"],
			&[type="email"],
			&[type="tel"],
			&[type="url"],
			&[type="password"] {
				outline: 0;
				margin-bottom: 30px;
				&:focus {
					border-color: var(--tj-color-theme-primary);
				}
			}
		}

		// & select {
		// 	display: inline-block !important;
		// }

		& .nice-select {
			// display: none;
			height: 48px;
			line-height: 36px;
			border-radius: 0;
			border: 1px solid var(--tj-color-theme-dark);
			color: var(--tj-color-heading-primary);
			float: unset;
			padding: 5px 22px;
			margin-bottom: 30px;

			&::after {
				inset-inline-end: 26px;
			}
			& .list {
				max-height: 300px;
				overflow: auto;
			}
		}

		.select2-container .select2-selection--single .select2-selection__rendered {
			padding-inline-start: 25px;
			padding-inline-end: 35px;
		}
		.select2-container--default
			.select2-selection--single
			.select2-selection__arrow {
			top: 0;
			inset-inline-end: 17px;
			height: 46px;
		}

		& button[name="save_address"],
		& button[name="save_account_details"] {
			border-radius: 0;
			border: 0;
			outline: 0;
			font-size: 16px;
			font-weight: 500;
			color: var(--tj-color-common-white);
			padding: 11px 45px;
			background-color: var(--tj-color-heading-primary);
			margin-bottom: 15px;
			text-transform: capitalize;

			&:hover {
				background-color: var(--tj-color-theme-primary);
				color: var(--tj-color-common-white);
			}
		}

		& button[name="save_account_details"] {
			margin-top: 20px;
		}

		& .woocommerce-form-row {
			margin-bottom: 0;
		}

		& button.woocommerce-form-login__submit[type="submit"],
		& button.woocommerce-form-register__submit[name="register"] {
			border-radius: 0;
			border: 0;
			outline: 0;
			font-size: 16px;
			font-weight: 500;
			color: var(--tj-color-common-white);
			padding: 11px 45px;
			background-color: var(--tj-color-theme-primary);
			margin-bottom: 15px;
			display: block;
			&:hover {
				background-color: var(--tj-color-heading-primary);
				color: var(--tj-color-common-white);
			}
		}

		& .woocommerce-privacy-policy-text {
			& p {
				& a {
					color: var(--tj-color-theme-primary);

					&:hover {
						text-decoration: underline !important;
					}
				}
			}
		}
	}
	&-existing-login,
	&-coupon {
		margin-bottom: 80px;
		& label {
			position: static;
			border: 0;
			margin-bottom: 7px;
			display: inline-block;
			width: auto;
			color: var(--tj-color-heading-primary);
			& abbr {
				&.required {
					color: red;
					text-decoration: none;
				}
			}
		}

		& .woocommerce-info {
			font-size: 16px;
			color: var(--tj-color-heading-primary);
			display: inline-block;
			padding: 8px 26px;
			width: 100%;

			& a {
				color: var(--tj-color-theme-primary);
				position: relative;
				border-bottom: 1px solid var(--tj-color-theme-primary);
			}
		}

		& .checkout_coupon.woocommerce-form-coupon,
		& .woocommerce-form.woocommerce-form-login {
			margin-top: 14px;
			background-color: var(--tj-color-common-white);
			position: relative;
		}

		& input {
			&[type="text"],
			&[type="email"],
			&[type="tel"],
			&[type="url"],
			&[type="password"] {
				outline: 0;
				border-radius: 0;
				height: 46px;
				background: #ffffff;
				border: 1px solid #ced7e0;
				font-size: 14px;
				color: var(--tj-color-heading-primary);
				padding: 0 25px;
				line-height: 46px;
				margin-bottom: 0;
				margin-inline-end: 0;

				@media #{$md, $sm, $xs} {
					width: 100%;
				}
				&:focus {
					border-color: var(--tj-color-theme-primary);
				}
			}
		}

		&
			.woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme {
			margin-bottom: 15px;
			& input {
				display: none;
				&:checked {
					& ~ span {
						&::after {
							background-color: var(--tj-color-theme-primary);
							border-color: var(--tj-color-theme-primary);
						}
						&::before {
							visibility: visible;
							opacity: 1;
						}
					}
				}
			}

			& span {
				font-size: 16px;
				color: #55585b;
				position: relative;
				padding-inline-start: 26px;
				z-index: 1;
				&::after {
					position: absolute;
					content: "";
					top: 2px;
					inset-inline-start: 0;
					width: 18px;
					height: 18px;
					line-height: 16px;
					text-align: center;
					border: 1px solid #c3c7c9;
					z-index: -1;
				}
				&::before {
					position: absolute;
					content: url("../icons/check.svg");
					top: 2px;
					inset-inline-start: 0;
					width: 18px;
					height: 18px;
					line-height: 16px;
					text-align: center;
					visibility: hidden;
					opacity: 0;
					color: var(--tj-color-common-white);
				}

				& a {
					&:hover {
						color: var(--tj-color-theme-primary);
					}
				}

				&:hover {
					cursor: pointer;
				}
			}
		}

		& .lost_password {
			& a {
				color: var(--tj-color-heading-primary);
				transition: all 0.3s ease-out 0s;
				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}
		}
	}
}

.woocommerce-form.woocommerce-form-login {
	&
		.woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme {
		margin-bottom: 15px;
		& input {
			display: none;
			&:checked {
				& ~ span {
					&::after {
						background-color: var(--tj-color-theme-primary);
						border-color: var(--tj-color-theme-primary);
					}
					&::before {
						visibility: visible;
						opacity: 1;
					}
				}
			}
		}

		& span {
			font-size: 16px;
			color: #676e7a;
			position: relative;
			padding-inline-start: 26px;
			z-index: 1;
			&::after {
				position: absolute;
				content: "";
				top: 5px;
				inset-inline-start: 0;
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				border: 1px solid var(--tj-color-border-2);
				z-index: -1;
			}
			&::before {
				position: absolute;
				content: "\f00c";
				inset-inline-start: 4px;
				top: 5px;
				text-align: center;
				visibility: hidden;
				opacity: 0;
				color: var(--tj-color-common-white);
				font-family: "Font Awesome 6 Pro";
				font-size: 10px;
			}

			& a {
				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}

			&:hover {
				cursor: pointer;
			}
		}
	}

	& .lost_password {
		& a {
			color: var(--tj-color-heading-primary);
			transition: all 0.3s ease-out 0s;
			&:hover {
				color: var(--tj-color-theme-primary);
			}
		}
	}
}

.woocommerce-MyAccount-content
	.woosw-list
	.woosw-items
	.woosw-item--add
	.add_to_cart_button {
	font-size: 16px;

	& svg {
		margin-inline-end: 0;
	}
}
.woocommerce-MyAccount-content table.woosc_table thead th,
.woocommerce-MyAccount-content table.woosc_table tbody td {
	padding: 10px;
	&.th-placeholder,
	&.td-placeholder {
		display: none;
	}
}
.woocommerce-MyAccount-content table.woosc_table thead th {
	a {
		font-weight: 500;
	}
	span {
		transition: all 0.3s ease-out 0s;
		&:hover {
			color: var(--tj-color-theme-primary);
		}
	}
}
.cart-wrapper {
	.woocommerce-checkout-review-order {
		table {
			margin-bottom: 0;
			border: 1px solid var(--tj-color-border-2);
		}
	}
}
.woocommerce-checkout-payment {
	margin-top: 0;

	& .wc_payment_methods {
		border: 1px solid var(--tj-color-border-2);
		border-top: 0;
		padding: 30px;
		padding-bottom: 30px;
		padding-top: 15px;
		& .wc_payment_method {
			list-style: none;
			padding-top: 0;
			&:not(:last-child) {
				margin-bottom: 10px;
			}
			& input {
				display: none;

				&:checked {
					& ~ label {
						&::before {
							opacity: 1;
							visibility: visible;
						}
					}
				}
			}
			& label {
				font-family: var(--tj-ff-heading);
				font-size: 16px;
				position: relative;
				padding-inline-start: 25px;
				font-weight: 500;
				color: var(--tj-color-text-body);

				&:hover {
					cursor: pointer;
				}

				&::after {
					position: absolute;
					content: "";
					inset-inline-start: 0;
					top: 3px;
					width: 20px;
					height: 20px;
					border-radius: 50%;
					border: 2px solid var(--tj-color-theme-dark);
				}
				&::before {
					position: absolute;
					content: "";
					inset-inline-start: 5px;
					top: 8px;
					width: 10px;
					height: 10px;
					border-radius: 50%;
					background-color: var(--tj-color-theme-dark);
					visibility: hidden;
					opacity: 0;
				}

				& img {
					margin-inline-start: 14px;
					@include transform(translateY(-2px));
				}

				& a {
					margin-inline-start: 20px;
					position: relative;
					&::after {
						position: absolute;
						content: "";
						inset-inline-start: 0;
						bottom: 3px;
						width: 100%;
						height: 1px;
						background-color: var(--tj-color-heading-primary);
					}
				}
			}

			& .payment_box {
				position: relative;
				padding-top: 10px;
				display: none;

				&::after {
					position: absolute;
					content: "";
					inset-inline-start: 57px;
					top: 0;
					width: 16px;
					height: 16px;
					background-color: #f6f7f9;
					@include transform(translateY(3px) rotate(45deg));
				}
				& p {
					background-color: #f6f7f9;
					padding: 10px 15px;
					margin-inline-start: 25px;
					font-size: 14px;
					line-height: 1.57;
					color: #55585b;
					margin-bottom: 0;
					padding-bottom: 0;
				}
			}
		}
	}

	.woocommerce-privacy-policy-text {
		margin-bottom: 25px;
	}
}
/*checkcout */
#customer_details .woocommerce-billing-fields__field-wrapper,
#customer_details .woocommerce-additional-fields__field-wrapper {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	margin-inline-start: -10px;
	margin-inline-end: -10px;
}
#customer_details .woocommerce-billing-fields__field-wrapper .form-row,
#customer_details .woocommerce-additional-fields__field-wrapper .form-row {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 100%;
	flex: 0 0 100%;
	max-width: 100%;
	padding-inline-start: 10px;
	padding-inline-end: 10px;
	margin-inline-start: 0;
	margin-inline-end: 0;
	margin-bottom: 0;
}
// @media (min-width: 768px) {
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_address_2_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_address_1_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_city_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_country_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_state_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_postcode_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_first_name_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_last_name_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_phone_field,
//   #customer_details .woocommerce-billing-fields__field-wrapper .form-row#billing_email_field,
//   #customer_details .woocommerce-additional-fields__field-wrapper .form-row#billing_first_name_field,
//   #customer_details .woocommerce-additional-fields__field-wrapper .form-row#billing_last_name_field,
//   #customer_details .woocommerce-additional-fields__field-wrapper .form-row#billing_phone_field,
//   #customer_details .woocommerce-additional-fields__field-wrapper .form-row#billing_email_field {
//     -webkit-box-flex: 0;
//     -ms-flex: 0 0 50%;
//     flex: 0 0 50%;
//     max-width: 50%;
//   }
// }
#billing_address_1_field {
	margin-bottom: 15px;
	// margin-top: -20px;
}
// #billing_postcode_field {
// 	margin-top: -20px;
// }
.woocommerce-checkout-review-order-table {
	th {
		padding: 12px 12px;
	}
}

.woocommerce-additional-fields h3 {
	margin-bottom: 20px;
}

.page-main-content table,
.page-main-content th,
.page-main-content td {
	border: 1px solid #ddd;
}

.woocommerce-MyAccount-navigation {
	margin-bottom: 30px;
}
.woosw-popup {
	.woosw-popup-inner .woosw-popup-content {
		max-width: 500px;
		a.wc-forward,
		.tj-cart-btn {
			width: 100%;
		}
		a.tj-cart-btn {
			.btn-text {
				display: none;
			}
		}
		a.wc-forward {
			font-size: 0;
		}
	}
}

.rg-15 {
	row-gap: 15px;
}

// quick view
.woosq-popup {
	.woosq-product {
		h3 {
			font-size: 28px;
		}
		.thumbnails img {
			object-fit: cover;
			margin: 0;
			width: 100%;
		}
		.price {
			display: inline-flex;
			align-items: center;
			flex-direction: row-reverse;
			gap: 5px;
			span {
				font-weight: 500;
				font-size: 16px;
				color: var(--tj-color-body-text);
				text-decoration: none;
			}
			del,
			ins {
				text-decoration: none;
			}
			del {
				span {
					text-decoration-line: line-through;
					color: var(--tj-color-common-black-2);
				}
			}
		}
	}
}

div.woocommerce {
	.woocommerce-cart-form table.shop_table {
		td:before {
			content: attr(data-title);
			position: absolute;
			inset-inline-start: 15px;
			top: 50%;
			vertical-align: top;
			padding: 0;
			-webkit-transform: translateY(-50%);
			transform: translateY(-50%);
			display: none;
			color: var(--tj-color-heading-primary);
			font-family: var(--tj-ff-heading);
			font-size: 16px;
			font-weight: 600;
			letter-spacing: -0.025em;
			line-height: 1.2;
		}
		@media #{$sm, $xs} {
			border: 0;
			thead {
				display: none;
			}
			tbody tr {
				border: 0;
				&:last-child {
					border-bottom: 1px solid var(--tj-color-border-2);
				}
			}
			td {
				position: relative;
			}
			td {
				padding: 15px;
				display: block;
				width: 100%;
				padding-inline-start: 25%;
				text-align: end;
				border: 1px solid var(--tj-color-border-2);
				border-bottom: 0;
				&.product-subtotal,
				&:first-child {
					border-inline-end: 1px solid var(--tj-color-border-2);
				}
				&.product-remove,
				&.product-name h5 {
					text-align: end;
				}
				&.product-quantity .tj-product-quantity {
					width: 136px;
					text-align: end;
					margin-inline-start: auto;
				}
			}
			td::before {
				display: block;
			}
			tbody tr td.product-thumbnail {
				width: auto;
				img {
					width: 100px;
				}
			}
		}
	}
}

.select2-container--default
	.select2-results__option--highlighted[aria-selected],
.select2-container--default
	.select2-results__option--highlighted[data-selected] {
	background-color: var(--tj-color-theme-primary);
	color: var(--tj-color-common-white);
}
