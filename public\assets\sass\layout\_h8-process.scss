@use "../utilities" as *;

/* START: Process CSS */
.h8-process {
	overflow: hidden;
	&-inner {
		display: flex;

		gap: 30px;
		position: relative;
		z-index: 0;
		@media #{ $md} {
			gap: 15px;
		}
		@media #{$sm,$xs} {
			gap: 20px;
			flex-direction: column;
		}
		.process {
			&-line {
				position: absolute;
				width: 100%;
				height: 1px;
				background-color: var(--tj-color-border-2);
				top: 48px;
				inset-inline-start: 0;

				z-index: 0;
				@media #{$sm,$xs} {
					width: 1px;
					height: 100%;
					top: 0px;
					inset-inline-start: 50%;
					transform: translateX(-50%);
				}
				&-active {
					position: absolute;
					width: 33.333%;
					height: 100%;
					background-color: var(--tj-color-border-1);
					top: 0;
					inset-inline-start: 0;
					transition: all 0.4s;
					z-index: 1;
					@media #{$sm,$xs} {
						width: 100%;
						height: 33.333%;
					}
				}
			}
			&-item {
				&.style-4 {
					position: relative;
					display: flex;
					align-items: center;
					flex-direction: column;
					z-index: 1;
					width: 33.33%;
					@media #{$sm,$xs} {
						width: 100%;
					}

					&::before {
						display: none;
					}
					.process-index {
						margin: 0;
						max-width: 98px;
						width: 100%;
						height: 112px;
						flex-shrink: 0;
						mask-image: url(../images/shapes/h8-process.svg);
						mask-size: cover;
						mask-repeat: no-repeat;
						mask-position: center;
						background-color: var(--tj-color-theme-bg);
						position: relative;
						display: inline-flex;
						justify-content: center;
						align-items: center;
						margin-bottom: -1px;
						z-index: 1;
						transition: all 0.4s;
						@media #{$lg,$md,$sm, $xs} {
							max-width: 80px;
							height: 91px;
						}
						@media #{$md, $sm, $xs} {
							margin-bottom: -1px;
						}

						span {
							width: 68px;
							height: 68px;
							background-color: var(--tj-color-theme-dark);
							color: var(--tj-color-common-white);
							border-radius: 100%;
							font-size: 20px;
							font-weight: var(--tj-fw-sbold);
							letter-spacing: -0.025em;
							line-height: 1;
							display: inline-flex;
							justify-content: center;
							align-items: center;
							margin-bottom: 14px;
							transition: all 0.4s;
							@media #{$lg,$md,$sm, $xs} {
								width: 62px;
								height: 62px;
							}
							@media #{ $xs} {
								font-size: 17px;
								margin-bottom: 10px;
							}
						}
					}
					.process-content {
						padding: 0;
						background-color: var(--tj-color-theme-bg);
						padding: 50px 30px;
						transition: all 0.3s;
						@media #{$lg,$md,$sm, $xs} {
							padding: 30px 25px;
						}

						.title {
							margin-bottom: 18px;
							letter-spacing: -0.025em;
						}
						.desc {
							line-height: 1.5;
							margin-bottom: 35px;
							@media #{$lg,$md,$sm, $xs} {
								margin-bottom: 20px;
							}
						}
						.service-button {
							&.text-btn {
								gap: 0;
							}
						}
					}
				}

				&.active {
					.process {
						&-index {
							background-color: var(--tj-color-theme-primary);
							span {
								background-color: var(--tj-color-common-white);
								color: var(--tj-color-theme-dark);
							}
						}
						&-content {
							background-color: var(--tj-color-theme-primary);
							.title {
								color: var(--tj-color-common-white);
							}
							.desc {
								color: var(--tj-color-common-white);
							}
							.service-button {
								color: var(--tj-color-common-white);
								&::before {
									background-color: var(--tj-color-common-white);
								}
							}
						}
					}
				}
			}
		}
	}
}

/* !END: Process CSS */
