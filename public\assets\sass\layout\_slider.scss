@use "../utilities" as *;

/* !START: Slider CSS */
.tj-slider-section {
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
	position: relative;
	overflow: hidden;
	z-index: 2;
	&::before {
		position: absolute;
		content: "";
		width: 80%;
		height: 100%;
		inset-inline-start: 0;
		top: 0;
		background: linear-gradient(
			90deg,
			var(--tj-color-theme-dark) 0%,
			rgba(5, 18, 41, 0) 100%
		);
		mix-blend-mode: multiply;
		z-index: -1;
	}
}
.slider-wrapper {
	display: flex;
	flex-wrap: wrap;
	align-items: end;
	position: relative;
	.slider-content {
		padding-top: 228px;
		padding-bottom: 300px;
		max-width: 700px;
		width: 100%;
		@media #{$xxl, $xl} {
			padding-top: 200px;
			padding-bottom: 250px;
		}
		@media #{$lg} {
			max-width: 550px;
			padding-top: 180px;
			padding-bottom: 220px;
		}
		@media #{$md} {
			padding-top: 150px;
			padding-bottom: 240px;
		}
		@media #{$sm, $xs} {
			padding-top: 150px;
			padding-bottom: 150px;
		}
		.sub-title {
			display: inline-block;
			font-size: 12px;
			font-weight: var(--tj-fw-bold);
			color: var(--tj-color-text-body-2);
			background-color: rgba(247, 247, 247, 0.1);
			text-transform: uppercase;
			padding: 3px 12px;
			margin-bottom: 25px;
			border-radius: 30px;
		}
		.slider-title {
			color: var(--tj-color-common-white);
			letter-spacing: -0.025em;
			line-height: 1.1;
			margin-bottom: 0;
		}
		.slider-button {
			margin-top: 45px;
			background-color: var(--tj-color-common-white);

			.btn_text {
				color: var(--tj-color-heading-primary);
			}

			&:hover {
				.btn_text {
					color: var(--tj-color-common-white);
				}
			}
			@media #{$xs} {
				margin-top: 30px;
			}
		}
	}
	.slider-feature-box {
		max-width: 440px;
		width: 100%;
		background: rgba(247, 247, 247, 0.1);
		backdrop-filter: blur(35px);
		padding: 45px 40px;
		position: absolute;
		inset-inline-end: 0;
		bottom: 0;
		z-index: 1;
		display: flex;
		flex-direction: column;
		gap: 30px;
		@media #{$lg, $md} {
			max-width: 410px;
			padding: 35px 30px;
			gap: 20px;
		}
		@media #{$sm, $xs} {
			position: relative;
			max-width: 75%;
			flex-direction: row;
			padding: 25px 20px;
			margin-inline-start: auto;
			margin-inline-end: -35px;
			gap: 20px;
		}
		@media #{$xs} {
			margin: 0 auto;
			max-width: 100%;
		}
		.slider-logo {
			width: 80px;
			@media #{$lg, $md} {
				width: 70px;
			}
			@media #{$sm, $xs} {
				width: 70px;
				flex: 0 0 auto;
			}
		}
		.desc {
			font-size: 18px;
			color: var(--tj-color-common-white);
			font-weight: var(--tj-fw-regular);
			line-height: 1.5;
			p {
				&:last-child {
					margin-bottom: 0;
				}
			}
			@media #{$xs} {
				font-size: 16px;
			}
		}
		.slider-shape {
			position: absolute;
			bottom: 0;
			inset-inline-end: 26px;
			z-index: -1;
			height: 100%;
			img {
				height: 100%;
			}
		}
	}
}
.tj-hero-slider.style-1 .sub-title {
	transform: translateY(-50px);
	transition: all 0.9s;
	transition-delay: 1s;
	opacity: 0;
}
.tj-hero-slider.style-1 .slider-title {
	transform: translateY(50px);
	transition: all 1s;
	transition-delay: 1.5s;
	opacity: 0;
	@media #{$xs} {
		font-size: 45px;
	}
}
.tj-hero-slider.style-1 .slider-button {
	transform: translateY(50px);
	transition: all 1.5s;
	transition-delay: 1.8s;
	opacity: 0;
}
.tj-hero-slider.style-1 .swiper-slide-active .slider-title {
	transform: translateY(0);
	opacity: 1;
}
.tj-hero-slider.style-1 .swiper-slide-active .slider-button,
.tj-hero-slider.style-1 .swiper-slide-active .sub-title {
	transform: translateY(0);
	opacity: 1;
}
.tj-hero-slider.style-1 .tj-navigation {
	position: absolute;
	bottom: 80px;
	z-index: 3;
	display: flex;
	gap: 5px;
	inset-inline-start: 0;
	inset-inline-end: 0;
	max-width: 1320px;
	margin: 0 auto;
	@media #{$xxl} {
		bottom: 110px;
	}
	@media #{$xl} {
		max-width: 1140px;
	}
	@media #{$lg} {
		max-width: 960px;
		bottom: 60px;
	}
	@media #{$md} {
		max-width: 720px;
	}
	@media #{$sm} {
		max-width: 540px;
	}
	@media #{$xs} {
		padding: 0 12px;
		bottom: 25%;
	}
}
.tj-hero-slider.style-1 .tj-btn {
	color: var(--tj-color-common-white-2);
	font-size: 20px;
	font-weight: 500;
	line-height: 1.3;
	text-transform: uppercase;
	display: flex;
	align-items: center;
	gap: 10px;
	&:hover {
		color: var(--tj-color-common-white);
	}
}

.tj-hero-slider.style-1 .tj-sw-pagination {
	width: 160px;
	z-index: 2;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 10px;
	color: var(--tj-color-common-white);
	font-size: large;
	font-weight: 500;
	text-transform: uppercase;
	@media #{$sm} {
		width: 130px;
	}
}
.tj-hero-slider.style-1 .tj-sw-pagination .dash {
	width: 100%;
	height: 1px;
	background: rgba(255, 255, 255, 0.2);
	display: inline-block;
	position: relative;
	.dash-inner {
		position: absolute;
		content: "";
		top: -1.5px;
		inset-inline-start: 0;
		height: 3px;
		background: var(--tj-color-common-white);
		display: inline-block;
		transition: width 0.3s ease;
	}
}
/* !END: Slider CSS */
