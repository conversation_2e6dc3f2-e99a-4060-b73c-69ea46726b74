@use "../utilities" as *;
/*----------------------------------------*/
/*  3. PRODUCT CSS START
/*----------------------------------------*/

.row {
	--bs-gutter-x: 30px;
}
.tj-product {
	$self: &;
	&-item {
		&:hover {
			#{$self} {
				&-action {
					visibility: visible;
					opacity: 1;
					inset-inline-end: 15px;
				}
				&-cart-btn {
					transform: translateY(-15px);
					opacity: 1;
					visibility: visible;
				}
			}
		}
	}
	&-thumb {
		position: relative;
		border-radius: 0;
		background-color: var(--tj-color-theme-bg);
		overflow: hidden;
		margin-bottom: 25px;
		img {
			width: 100%;
		}
	}
	&-badge {
		position: absolute;
		top: 15px;
		inset-inline-start: 15px;
		z-index: 1;
		& span {
			font-weight: 400;
			font-size: 14px;
			line-height: 1;
			color: var(--tj-color-common-white);
			background-color: var(--tj-color-theme-primary);
			border-radius: 40px;
			display: inline-block;
			padding: 6px 10px;
			&.sold-out {
				background-color: #ff0004;
			}
		}
	}
	&-title {
		font-size: 20px;
		margin-bottom: 5px;
		& a {
			&:hover {
				color: var(--tj-color-theme-primary);
			}
		}
	}
	&-tag {
		margin-bottom: 2px;
		& a {
			font-size: 14px;
			position: relative;
			display: inline-block;
			line-height: 1;
			&::after {
				position: absolute;
				content: "";
				inset-inline-start: auto;
				inset-inline-end: 0;
				bottom: 0;
				width: 0;
				height: 1px;
				background-color: var(--tj-color-theme-primary);
				transition: all 0.3s ease-in-out;
			}

			&:hover {
				color: var(--tj-color-theme-primary);
				&::after {
					inset-inline-start: 0;
					inset-inline-end: auto;
					width: 100%;
				}
			}
		}
	}
	&-price-wrapper {
		.price {
			display: inline-flex;
			align-items: center;
			flex-direction: row-reverse;
			gap: 5px;
			span {
				font-weight: 500;
				font-size: 16px;
				color: var(--tj-color-body-text);
				text-decoration: none;
			}
			del,
			ins {
				text-decoration: none;
			}
			del {
				span {
					text-decoration-line: line-through;
					color: var(--tj-color-common-black-2);
				}
			}
		}
	}
	&-action {
		position: absolute;
		inset-inline-end: -50px;
		top: 15px;
		z-index: 1;
		visibility: hidden;
		opacity: 0;
		transition: all 0.3s ease-in-out;
		&-item {
			gap: 10px;
		}
		&-btn {
			$self: &;
			position: relative;
			& a,
			& button {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 42px;
				height: 42px;
				line-height: 42px;
				text-align: center;
				font-size: 0;
				color: var(--tj-color-theme-dark);
				background-color: var(--tj-color-common-white);
				border-bottom: 0;
				box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
				border-radius: 50%;
				transition: all 0.3s ease-out 0s;
				i {
					font-size: 16px;
				}
				&:hover,
				&.loading:hover {
					color: var(--tj-color-common-white);
					background-color: var(--tj-color-theme-dark);
					&::before,
					&::after {
						color: var(--tj-color-common-white) !important;
					}
				}
			}
			button::before {
				font-size: 16px;
				content: "\f004";
				font-family: var(--tj-ff-fontawesome);
				margin: 0;
				font-weight: 300;
				display: inline-block;
				&.woosw-btn.woosw-added {
					&::before {
						margin: 0;
					}
				}
			}
			&-tooltip {
				position: absolute;
				top: 50%;
				@include transform(translateY(-50%));
				inset-inline-end: 60px;
				font-weight: 500;
				font-size: 14px;
				color: var(--tj-color-common-white);
				background-color: var(--tj-color-theme-dark);
				z-index: 1;
				width: max-content;
				line-height: 1;
				padding: 8px 10px;
				visibility: hidden;
				opacity: 0;
				transition: all 0.3s ease-in-out;
				&::after {
					position: absolute;
					content: "";
					inset-inline-end: -10px;
					top: 50%;
					transform: translate(-50%, -50%) rotate(45deg);
					height: 10px;
					width: 10px;
					background-color: var(--tj-color-theme-dark);
				}
			}
			&:hover {
				#{$self} {
					&-tooltip {
						visibility: visible;
						opacity: 1;
					}
				}
			}
		}
	}
	&-rating-icon {
		margin-bottom: 5px;
	}
	&-cart-btn {
		position: absolute;
		bottom: 0;
		inset-inline-start: 0;
		inset-inline-end: 0;
		transform: translateY(60px);
		opacity: 0;
		visibility: hidden;
		transition: all 0.3s ease-out 0s;
	}
}
.tj-product-area {
	overflow: hidden;
}
.tj-product-rating {
	margin-top: 3px;
}
.woosw-list table a.wc-forward,
.woosw-popup a.wc-forward,
.tj-product-item .wc-forward,
.tj-cart-btn {
	width: calc(100% - 30px);
	margin: 0 auto;
	height: 48px;
	padding: 0 30px;
	color: var(--tj-color-common-white);
	background: var(--tj-color-theme-dark);
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	span.btn-icon {
		overflow: hidden;
		position: relative;
		display: inline-flex;
		color: var(--tj-color-common-white);
		i {
			&:first-child,
			&:last-child {
				transition: transform 0.4s ease-in-out 0s;
			}
		}
		i:last-child {
			position: absolute;
			transform: translateX(-150%);
		}
	}

	span.btn-text {
		display: inline-flex;
		overflow: hidden;
		color: var(--tj-color-common-white);
		text-shadow: 0 23px 0 currentColor;
		font-weight: 700;
		> span {
			display: flex;
			align-items: center;
			backface-visibility: hidden;
			transform: translateY(0);
			transition: 0.5s;
		}
	}
	&:hover {
		background: var(--tj-color-theme-primary);
		span.btn-icon {
			color: var(--tj-color-common-white);
			i:first-child {
				transform: translateX(150%);
			}
			i:last-child {
				transform: translateX(0);
			}
		}
		span.btn-text {
			color: var(--tj-color-common-white);
			> span {
				transform: translateY(-24px);
			}
		}
	}
}
.tj-shop-listing-popup .nice-select {
	border-color: var(--tj-color-theme-bg);
}

.tj-shop-sidebar .ui-widget-content {
	border: 0;
	border-radius: 0;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-thumbnail img {
	width: 92px;
	// height: 92px;
	object-fit: cover;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-remove a {
	font-weight: var(--tj-fw-bold);
}
div.woocommerce .cart_totals table {
	margin-bottom: 30px;
}

.woosq-product.container {
	--bs-gutter-x: 0;
	@media #{$xs} {
		margin: 0 15px;
		width: calc(100% - 30px);
	}
	.row {
		--bs-gutter-x: 0;
	}
}
.vbox-overlay:has(.woosq-product.container) {
	.swiper-button {
		&-prev,
		&-next {
			width: 44px;
			height: 44px;
			line-height: 44px;
			text-align: center;
			padding: 0 0 0 0;
			margin: 0 0 0 0;
			overflow: hidden;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			font-size: 0;
			color: #222;
			background-color: transparent;
			border-radius: 0;
			border: none !important;
			z-index: 8;
			&::after {
				font-size: 15px;
				line-height: 44px;
				font-weight: 800;
			}
			&:hover {
				color: #fff;
				background-color: rgba(0, 0, 0, 0.5);
			}
		}
		&-prev {
			left: 0;
		}
		&-next {
			right: 0;
		}
	}
	.swiper-pagination-bullet {
		background-color: var(--tj-color-common-black);
		opacity: 0.5;
		&-active {
			opacity: 1;
		}
	}
	.vbox-backdrop {
		background-color: rgba(23, 23, 23, 0.8) !important;
	}
}
.vbox-content:has(.woosq-product.container) {
	max-width: 920px;
	margin: 0 auto;
	padding: 0;
	.vbox-child.vbox-inline {
		background-color: transparent !important;
		box-shadow: none;
	}
}
.woosq-product.container .thumbnails {
	.images {
		max-width: 460px;
	}
}
.woosq-product.container {
	background-color: #ffffff;
	box-shadow: 0 0 12px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
	.thumbnail {
		img {
			height: 460px;
			width: 100%;
			object-fit: cover;
		}
	}
}
.woosq-product.container .summary .summary-content {
	padding: 20px;
}
.tj-product-details-action-item-wrapper {
	flex-wrap: wrap;
}
.woosq-product.container .price {
	display: inline-flex;
	align-items: center;
	flex-direction: row-reverse;
	gap: 5px;
}
