@use "../utilities" as *;

/* START: Skill CSS */
.tj-skill-section {
	padding-top: 325px;
	padding-bottom: 120px;
	background-color: var(--tj-color-theme-bg);
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	@media #{$xl} {
		padding-top: 250px;
	}
	@media #{$lg} {
		padding-top: 220px;
		padding-bottom: 100px;
	}
	@media #{$md} {
		padding-top: 150px;
		padding-bottom: 80px;
	}
	@media #{$sm} {
		padding-top: 80px;
		padding-bottom: 80px;
	}
	@media #{$xs} {
		padding-top: 80px;
		padding-bottom: 80px;
	}
}
.skill-wrapper {
	backdrop-filter: blur(17.5px);
	background: rgba(247, 247, 247, 0.1);
	max-width: 525px;
	width: 100%;
	margin-inline-start: auto;
	padding: 40px;
	.title {
		color: var(--tj-color-common-white);
		letter-spacing: -0.025em;
		margin-bottom: 15px;
	}
	.desc {
		color: var(--tj-color-common-white-2);
		margin-bottom: 35px;
		p {
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
	@media #{$md} {
		max-width: 475px;
		padding: 30px 15px 35px;
	}
	@media #{$sm} {
		max-width: 465px;
		padding: 30px 15px 35px;
	}
	@media #{$xs} {
		max-width: 465px;
		padding: 30px 15px 35px;
		.desc {
			margin-bottom: 25px;
		}
	}
}
.tj-progress-bar {
	.progress-item {
		position: relative;
		margin-top: 20px;
		z-index: 1;
		&:first-child {
			margin-top: 0px;
		}
		.title {
			font-size: 16px;
			font-weight: var(--tj-fw-sbold);
			letter-spacing: -0.025em;
			margin-bottom: 10px;
		}
		.item_bar {
			position: relative;
			height: 8px;
			width: 100%;
			background-color: rgba(247, 247, 247, 0.2);
			margin-inline-start: 0;
			.progress {
				position: absolute;
				inset-inline-start: 0;
				top: 0;
				bottom: 0;
				width: 0;
				height: 8px;
				margin: 0;
				border-radius: 0px;
				overflow: visible;
				background-color: var(--tj-color-theme-primary);
				transition: width 100ms ease;
				&::before {
					position: absolute;
					content: "";
					width: 18px;
					height: 18px;
					top: 50%;
					transform: translateY(-50%);
					border: 4px solid var(--tj-color-theme-primary);
					background-color: var(--tj-color-common-white);
					border-radius: 50%;
					inset-inline-end: -10px;
				}
				&::after {
					content: attr(data-value);
					position: absolute;
					inset-inline-end: -20px;
					top: -32px;
					font-size: 16px;
					font-weight: 600;
					color: var(--tj-color-common-white);
				}
			}
		}
	}
}
/* !END: Skill CSS */
