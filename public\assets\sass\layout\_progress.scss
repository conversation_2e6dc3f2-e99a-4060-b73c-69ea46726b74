@use "../utilities" as *;

/* START: Progress CSS */
.tj-progress-section {
	position: relative;
	background: var(--tj-color-theme-bg);
	z-index: 2;
	.sec-heading {
		margin-bottom: 0;
	}
	.progress-bg-images {
		position: absolute;
		width: 50%;
		height: 100%;
		inset-inline-start: 0;
		top: 0;
		background-repeat: no-repeat;
		background-position: center;
		background-size: cover;
		z-index: -1;
		&::before {
			position: absolute;
			content: "";
			width: 100%;
			height: 100%;
			top: 0;
			inset-inline-start: 0;
			background: rgba(5, 18, 41, 0.85);
		}
	}
}
.progress-images {
	@media #{$md, $sm, $xs} {
		margin-top: 40px;
		img {
			width: 100%;
		}
	}
}
.progress-right-content {
	padding-inline-start: 95px;
	@media #{$xl} {
		padding-inline-start: 30px;
	}
	@media #{$lg} {
		padding-inline-start: 25px;
	}
	@media #{$md, $sm, $xs} {
		padding-inline-start: 0;
	}
}
.progress-style-2 {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 80px;
	margin-top: 40px;
	.proggess-item {
		.proggess-circle {
			margin-bottom: 10px;
			input {
				color: var(--tj-color-heading-primary) !important;
				font-size: 22px !important;
				width: auto;
				font-weight: var(--tj-fw-bold) !important;
				font-family: var(--tj-ff-heading) !important;
			}
		}
		.proggess-text {
			text-align: center;
			.sub-title {
				display: block;
				font-size: 18px;
				font-family: var(--tj-ff-heading);
				font-weight: var(--tj-fw-regular);
				letter-spacing: initial;
				text-transform: unset;
				color: var(--tj-color-heading-primary);
				margin-bottom: 0px;
				line-height: 1.5;
			}
		}
	}
	@media #{$lg} {
		gap: 40px;
	}
	@media #{$xs} {
		gap: 30px;
		.proggess-item {
			.proggess-circle {
				canvas {
					width: 145px !important;
					height: 145px !important;
				}
				input {
					margin-top: 45px !important;
					margin-inline-start: -115px !important;
				}
			}
		}
	}
}
/* !END: Progress CSS */
