# Disable directory browsing
Options -Indexes

<IfModule mod_rewrite.c>
    RewriteEngine On

    # If the request is for a file or directory that exists in the root
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d

    # If the request is not already for the public directory
    RewriteCond %{REQUEST_URI} !^/public/

    # Redirect to public directory
    RewriteRule ^(.*)$ public/index.php/$1 [L]
</IfModule>

# Fallback for servers without mod_rewrite
<IfModule !mod_rewrite.c>
    ErrorDocument 404 /public/index.php
</IfModule>
