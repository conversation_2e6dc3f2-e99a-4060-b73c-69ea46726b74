@use "../utilities" as *;

/* START: Footer CSS */
.h10-footer {
	&::after {
		content: url("../images/shapes/h10-footer-shape.png");
		position: absolute;
		top: 0;
		inset-inline-end: 0;
	}
	&-top {
		padding-top: 105px;
		padding-bottom: 120px;
		@media #{$lg} {
			padding-top: 65px;
			padding-bottom: 80px;
		}
		@media #{$md, $sm, $xs} {
			padding: 80px 0;
		}
	}
	&.footer-2.style-2 .footer-top-area {
		.line::before {
			inset-inline-start: 34.2%;
			@media #{$lg} {
				display: block;
				inset-inline-start: 34.5%;
			}
			@media #{$md} {
				display: block;
				inset-inline-start: 51.5%;
			}
		}
	}
	&-newsletter-form {
		margin-top: 15px;
		@media #{$sm,$xs} {
			margin-top: 0;
		}
		.newsletter-form {
			max-width: 350px;
		}
	}

	&-cta {
		display: flex;
		align-items: center;
		gap: 15px;
		padding-bottom: 145px;
		@media #{$lg} {
			padding-bottom: 100px;
		}
		@media #{$md} {
			padding-bottom: 70px;
		}
		@media #{$sm,$xs} {
			padding-bottom: 70px;
		}
		&-title {
			color: var(--tj-color-common-white);
			margin-bottom: 0;
			@media #{$xl} {
				font-size: 63px;
			}

			@media #{$xs} {
				font-size: 30px;
			}
		}
		&-btn {
			width: 70px;
			height: 70px;
			background-color: var(--tj-color-theme-primary);
			font-size: 28px;
			flex-shrink: 0;
			@media #{$lg,$md} {
				width: 60px;
				height: 60px;
				font-size: 26px;
			}
			@media #{$sm,$xs} {
				width: 50px;
				height: 50px;
				font-size: 22px;
			}
			i {
				text-shadow: -50px 0 0;
			}

			&:hover {
				i {
					transform: rotate(-45deg) translateX(50px);
				}
			}
		}
	}
	&-widget {
		.desc {
			font-size: 18px;
			line-height: 1.5;
			margin-bottom: 26px;
		}
		&-wrapper {
			padding-inline-start: 60px;
			position: relative;

			@media #{$xl} {
				padding-inline-start: 25px;
			}
			@media #{$lg,$md,$sm,$xs} {
				padding-inline-start: 20px;
			}
			@media #{$sm,$xs} {
				padding-inline-start: 0;
			}
			&::before {
				position: absolute;
				content: "";
				width: 200%;
				height: 1px;
				top: 44%;
				inset-inline-start: 0;
				background: var(--tj-color-border-1);

				@media #{$lg} {
					top: 29%;
				}
				@media #{$md,$sm,$xs} {
					top: 26%;
				}
				@media #{$sm,$xs} {
					top: 19%;
					width: 100%;
				}
			}
			.footer-contact-infos-2 {
				padding-inline-start: 41px;
				@media #{$md,$sm,$xs} {
					padding-inline-start: 0;
				}
			}
			.footer-social {
				margin-top: 0;
				display: flex;
				justify-content: flex-end;
				@media #{$lg,$md,$sm,$xs} {
					justify-content: flex-start;
				}
			}
		}
	}
}

/* !END: Footer CSS */
