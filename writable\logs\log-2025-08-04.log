CRITICAL - 2025-08-04 02:29:29 --> ErrorException: Undefined variable $page
[Method: GET, Route: behind-the-build]
in APPPATH\Views\templates\header.php on line 149.
 1 APPPATH\Views\templates\header.php(149): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $page', 'C:\\xampp\\htdocs\\eastern3\\app\\Views\\templates\\header.php', 149)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\eastern3\\app\\Views\\templates\\header.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('templates/header', [], true)
 5 APPPATH\Controllers\Pages.php(36): view('templates/header', [...])
 6 APPPATH\Controllers\Pages.php(11): App\Controllers\Pages->renderPage('pages/behind_the_build', 'Behind the Build')
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Pages->behindTheBuild()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Pages))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-04 02:29:39 --> ErrorException: Undefined variable $page
[Method: GET, Route: behind-the-build]
in APPPATH\Views\templates\header.php on line 149.
 1 APPPATH\Views\templates\header.php(149): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $page', 'C:\\xampp\\htdocs\\eastern3\\app\\Views\\templates\\header.php', 149)
 2 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\eastern3\\app\\Views\\templates\\header.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('templates/header', [], true)
 5 APPPATH\Controllers\Pages.php(36): view('templates/header', [...])
 6 APPPATH\Controllers\Pages.php(11): App\Controllers\Pages->renderPage('pages/behind_the_build', 'Behind the Build')
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Pages->behindTheBuild()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Pages))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
