@use "../utilities" as *;

/* START: Search CSS */
.search {
	&_popup {
		position: fixed;
		top: 0;
		inset-inline-start: 0;
		width: 100%;
		background-color: var(--tj-color-theme-dark);
		z-index: 100;
		padding-top: 60px;
		padding-bottom: 90px;
		opacity: 0;
		@include transform(translateY(calc(-100% - 80px)));
		transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
		transition-delay: 0.5s;
		z-index: 9999;

		.search_close {
			position: absolute;
			top: 36px;
			inset-inline-end: 36px;
			.search_close_btn {
				font-size: 24px;
				line-height: 1;
				color: var(--tj-color-common-white);

				svg {
					width: 24px;
					height: 24px;
					@include transition(all 0.4s ease-in-out 0s);
					path {
						fill: var(--tj-color-common-white);
					}
					&:hover {
						transform: rotate(90deg);
						path {
							fill: var(--tj-color-theme-primary);
						}
					}
				}
			}
		}
		&.search-opened {
			opacity: 1;
			@include transform(translateY(0%));
			transition-delay: 0s;
			.search_form {
				.search_input {
					@include transform(translateY(0px));
					opacity: 1;
					transition-delay: 0.3s;
					&::after {
						width: 100%;
						transition-delay: 0.5s;
					}
				}
			}
		}
	}
}

.tj_search_wrapper {
	.search_form {
		form {
			.search_input {
				position: relative;
				z-index: 1;
				.title {
					color: var(--tj-color-common-white);
					margin-bottom: 30px;
				}
				.search-box {
					position: relative;
					z-index: 1;

					input[type="search"] {
						width: 100%;
						font-family: var(--tj-ff-body);
						color: var(--tj-color-common-white);
						border: 1px solid var(--tj-color-border-1);
						background: transparent;
						padding: 20px 75px 20px 24px;
						&::placeholder {
							color: var(--tj-color-common-white-2);
						}
					}
					button {
						display: inline-flex;
						align-items: center;
						justify-content: center;
						position: absolute;
						inset-inline-end: 0;
						top: 50%;
						transform: translateY(-50%);
						max-width: 62px;
						font-size: 22px;
						line-height: 1;
						color: var(--tj-color-common-white);
						border-inline-start: 1px solid var(--tj-color-border-1);
						width: 100%;
						height: 100%;

						i {
							display: inline-flex;
							line-height: 1;
						}
					}
				}
			}
		}
	}
}

.search-popup-overlay {
	position: fixed;
	inset-inline-start: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	z-index: 99;
	@include transform(translateY(calc(-100% - 80px)));
	transition: all 0.5s ease-in-out 0s;
	transition-delay: 0.3s;

	&.opened {
		@include transform(translateY(0));
	}
}
/* !END: Search CSS */
