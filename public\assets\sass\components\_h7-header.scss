@use "../utilities" as *;

/* START: Header CSS */

// header 7 css
.tj-header-area {
	&.header-7 {
		.container-fluid:not(.gap-60-25) {
			--bs-gutter-x: 0;
			> .row {
				--bs-gutter-x: 0;
			}
		}
		&.sticky {
			.container-fluid {
				--bs-gutter-x: 30px;
				> .row {
					--bs-gutter-x: 30px;
				}
			}
		}
		&.header-absolute {
			.header-wrapper {
				padding: 0px 30px;

				@media #{$md, $sm, $xs} {
					padding: 17px 15px;
				}
			}
		}
		.mainmenu {
			> ul {
				> li {
					> a {
						color: var(--tj-color-common-white);
					}
					&:hover,
					&.current-menu-ancestor {
						> a {
							color: var(--tj-color-theme-primary);
						}
					}
				}
			}
		}
		.header {
			&-bottom {
				padding: 0;
			}

			&_right_info {
				column-gap: 20px;
				> :not(:last-child) {
					padding-inline-end: 0;
					margin-inline-end: 0;
					&::after {
						display: none;
					}
				}
			}
			&_btn.tj-primary-btn {
				background-color: rgba(247, 247, 247, 0.1);
				backdrop-filter: blur(50px);
				padding-inline-end: 4.5px;
				margin-inline-end: 0;

				.btn_text {
					color: var(--tj-color-common-white);
				}
			}

			&_search {
				.icon {
					height: 48px;
					width: 48px;
				}
				i {
					font-size: 17px;
				}
			}
		}
		.tj-demo-button {
			.header_btn.tj-primary-btn {
				background-color: var(--tj-color-common-white);
				.btn_text {
					color: var(--tj-color-heading-primary);
					text-shadow: 0 23px 0 var(--tj-color-common-white);
				}
			}
		}
	}

	// mobile toggler
	.menu_btn {
		.line {
			width: 50px;
			height: auto;
			display: inline-flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-end;
			gap: 8px;

			border-radius: var(--tj-br-md);

			span {
				width: 100%;
				height: 1px;
				background-color: var(--tj-color-common-white);
				flex-shrink: 0;

				&:nth-child(2) {
					width: 50%;
				}
			}

			&:hover {
				span {
					&:nth-child(2) {
						width: 100%;
					}
				}
			}
		}
	}
}

/* !END: Header CSS */
