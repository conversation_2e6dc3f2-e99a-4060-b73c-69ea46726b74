@use "../utilities" as *;

/* START: H8 Services CSS */
.h8-services {
	&-section {
		background-color: var(--tj-color-theme-dark);

		.tj-primary-btn {
			background-color: var(--tj-color-common-white);

			.btn_inner {
				.btn_text {
					color: var(--tj-color-heading-primary);
				}
			}

			&:hover {
				.btn_inner {
					.btn_text {
						color: var(--tj-color-common-white);
					}
				}
			}
		}
	}

	&-wrapper {
		display: flex;
		flex-wrap: wrap;
		gap: 60px;
	}
	&-left {
		max-width: calc(100% - 700px);
		width: 100%;

		&-content {
			margin-top: 55px;

			.title {
				max-width: 380px;
				width: 100%;
				color: var(--tj-color-common-white);
				letter-spacing: -0.025em;
				margin: 0;

				span {
					color: var(--tj-color-theme-primary);
				}
			}

			.tj-primary-btn {
				margin-top: 30px;
			}
		}
	}
	&-right {
		max-width: 640px;
		width: 100%;
	}

	&-list {
		border-top: 1px solid var(--tj-color-border-1);
		.service_item {
			border-bottom: 1px solid var(--tj-color-border-1);
			display: flex;
			flex-wrap: wrap;
			gap: 13px;
			align-items: start;
			padding: 45px 0;

			.no {
				display: inline-flex;
				font-family: var(--tj-ff-heading);
				font-weight: var(--tj-fw-sbold);
				font-size: 16px;
				line-height: 1.5;
				letter-spacing: -0.025em;
				color: var(--tj-color-common-white-2);
			}
			.content {
				max-width: 340px;
				width: 100%;

				.title {
					color: var(--tj-color-common-white);
					letter-spacing: -0.025em;
					margin: 0;

					a {
						&:hover {
							color: var(--tj-color-theme-primary);
						}
					}
				}
				.desc {
					color: var(--tj-color-common-white-2);
					margin-top: 15px;
				}
			}

			.service_btn {
				width: 70px;
				height: 70px;
				border: 1px solid var(--tj-color-border-1);
				background-color: var(--tj-color-theme-dark);
				font-size: 24px;
				margin-inline-start: auto;

				i,
				svg {
					text-shadow: -45px 0 0;
				}
			}

			&:hover {
				.service_btn {
					background-color: var(--tj-color-theme-primary);
					border-color: var(--tj-color-theme-primary);

					i,
					svg {
						transform: rotate(-45deg) translateX(45px);
					}

					&:hover {
						i,
						svg {
							transform: rotate(0deg) translateX(45px);
						}
					}
				}
			}
		}
	}

	@media #{$xl} {
		&-wrapper {
			gap: 30px;
		}
		&-left {
			max-width: calc(100% - 630px);
		}
		&-right {
			max-width: 600px;
		}
	}
	@media #{$lg} {
		&-wrapper {
			gap: 30px;
		}
		&-left {
			max-width: calc(100% - 530px);
		}
		&-right {
			max-width: 500px;
		}
	}
	@media #{$md, $sm, $xs} {
		&-wrapper {
			gap: 50px;

			.mobile_btn {
				margin-top: 50px;
			}
		}
		&-left {
			max-width: 100%;

			&-content {
				margin-top: 50px;

				.title {
					max-width: 100%;
				}
			}
		}
		&-right {
			max-width: 100%;
		}
		&-list {
			.service_item {
				padding: 36px 0;
			}
		}
	}
	@media #{$xs} {
		&-wrapper {
			.mobile_btn {
				margin-top: 40px;
			}
		}
		&-left {
			max-width: 100%;

			&-content {
				margin-top: 40px;
			}
		}
		&-list {
			.service_item {
				padding: 36px 0;
				gap: 20px;
				flex-direction: column;

				.service_btn {
					margin-inline-start: 0;
					width: 50px;
					height: 50px;
				}
			}
		}
	}
}

.h8-video {
	position: relative;
	z-index: 1;

	&::after {
		content: "";
		position: absolute;
		inset-inline-start: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(5, 18, 41, 0.3);
		z-index: 1;
	}
	img {
		width: 100%;
		min-height: 330px;
		object-fit: cover;
	}

	.play_btn {
		position: absolute;
		inset-inline-start: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		z-index: 2;
	}

	@media #{$lg, $md} {
		img {
			min-height: 300px;
		}
	}
}

.play_btn {
	width: 58px;
	height: 58px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	line-height: 1;
	background-color: var(--tj-color-common-white);
	color: var(--tj-color-theme-dark);
	border-radius: 50%;
	animation: pulse 2s ease infinite;

	i,
	svg {
		display: inline-flex;
		line-height: 1;
	}
}
/* !END: H8 Services CSS */
