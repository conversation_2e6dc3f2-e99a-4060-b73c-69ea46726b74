:root {
  /**
    @Font-Family Declaration
  */
  --tj-ff-body: 'Lato', sans-serif;
  --tj-ff-heading: 'Li<PERSON> Franklin', serif;
  --tj-ff-fontawesome: "Font Awesome 6 Pro";
  /**
    @Font-weight Declaration
  */
  --tj-fw-normal: normal;
  --tj-fw-thin: 100;
  --tj-fw-elight: 200;
  --tj-fw-light: 300;
  --tj-fw-regular: 400;
  --tj-fw-medium: 500;
  --tj-fw-sbold: 600;
  --tj-fw-bold: 700;
  --tj-fw-ebold: 800;
  --tj-fw-black: 900;
  /**
    @Font-Size Declaration
  */
  --tj-fs-body: 16px;
  --tj-fs-p: 16px;
  --tj-fs-h1: 72px;
  --tj-fs-h2: 48px;
  --tj-fs-h3: 32px;
  --tj-fs-h4: 24px;
  --tj-fs-h5: 20px;
  --tj-fs-h6: 18px;
  /**
    @Color Declaration
  */
  --tj-color-common-white: #f7f7f7;
  --tj-color-common-white-2: #a9b0b8;
  --tj-color-common-black: #000000;
  --tj-color-common-black-2: #676e7a;
  --tj-color-common-black-3: #969ca5;
  --tj-color-heading-primary: #051229;
  --tj-color-text-body: #364052;
  --tj-color-text-body-2: #7e8590;
  --tj-color-theme-primary: #0075ff;
  --tj-color-theme-dark: #051229;
  --tj-color-theme-bg: #e1e8f0;
  --tj-color-theme-bg-2: #dfecfd;
  --tj-color-border-1: #27354d;
  --tj-color-border-2: #ced7e0;
  --tj-color-border-3: #d7d8db;
  --tj-color-red-1: #ff0000;
}

/*----------------------------------------*/
/*  1. WOOCOMMERCE CSS START
/*----------------------------------------*/
figure {
  margin: 0;
}

.tj-product-details-bottom .tj-product-details-description h2 {
  font-size: 26px;
  color: var(--tj-color-heading-primary);
  font-weight: 600;
  margin-bottom: 25px;
}

span.required {
  color: var(--tj-color-text-body);
}

ul.woocommerce-error li {
  color: #ff0004;
}
ul.woocommerce-error li a {
  text-decoration: none;
}

.tj-product-details-description p:last-child {
  margin-bottom: 0;
}
.tj-product-details-description p.woocommerce-noreviews {
  margin-bottom: 25px;
}
.tj-product-details-description ol.commentlist {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  list-style: none;
}
.tj-product-details-description ol.commentlist li {
  margin-bottom: 15px;
}
.tj-product-details-description ol.commentlist .review .comment_container {
  overflow: hidden;
  border: 1px solid var(--tj-color-border-2);
  padding: 30px;
}
.tj-product-details-description ol.commentlist .review .comment_container img {
  float: left;
  overflow: hidden;
  border-radius: 50%;
}
.tj-product-details-description ol.commentlist .review .comment_container .comment-text {
  overflow: hidden;
  -webkit-margin-start: 75px;
          margin-inline-start: 75px;
}
.tj-product-details-description ol.commentlist .review .comment_container .comment-text .meta {
  margin-bottom: 5px;
}
.tj-product-details-description ol.commentlist .review .comment_container .comment-text p {
  margin-bottom: 0;
}
.tj-product-details-description ol.commentlist .review .comment_container .comment-text .star-rating {
  display: inline-block;
  padding: 3px 4px 3px 8px;
  background-color: var(--tj-color-theme-bg);
  border-radius: 32px;
}
.tj-product-details-description ol.commentlist .review .comment_container .comment-text .star-rating::before {
  position: absolute;
}
.tj-product-details-description ol.commentlist .review .comment_container .comment-text .star-rating span {
  position: unset;
}
.tj-product-details-description label {
  display: block;
  font-size: 16px;
}
.tj-product-details-description input:not([type=submit]), .tj-product-details-description textarea {
  padding: 0px 20px;
  width: 100%;
  height: 48px;
  border: 1px solid var(--tj-color-border-1);
  border-radius: 0;
  outline: 0;
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-text-body);
  background-color: transparent;
}
.tj-product-details-description input:not([type=submit]):focus, .tj-product-details-description textarea:focus {
  background-color: var(--tj-color-theme-bg);
}
.tj-product-details-description input:not([type=submit])::-webkit-input-placeholder, .tj-product-details-description textarea::-webkit-input-placeholder {
  color: var(--tj-color-text-body-2);
  font-weight: var(--tj-fw-medium);
}
.tj-product-details-description input:not([type=submit])::-moz-placeholder, .tj-product-details-description textarea::-moz-placeholder {
  color: var(--tj-color-text-body-2);
  font-weight: var(--tj-fw-medium);
}
.tj-product-details-description input:not([type=submit]):-ms-input-placeholder, .tj-product-details-description textarea:-ms-input-placeholder {
  color: var(--tj-color-text-body-2);
  font-weight: var(--tj-fw-medium);
}
.tj-product-details-description input:not([type=submit])::-ms-input-placeholder, .tj-product-details-description textarea::-ms-input-placeholder {
  color: var(--tj-color-text-body-2);
  font-weight: var(--tj-fw-medium);
}
.tj-product-details-description input:not([type=submit])::placeholder, .tj-product-details-description textarea::placeholder {
  color: var(--tj-color-text-body-2);
  font-weight: var(--tj-fw-medium);
}
.tj-product-details-description input:not([type=submit])::-ms-input-placeholder, .tj-product-details-description textarea::-ms-input-placeholder {
  color: var(--tj-color-text-body-2);
  font-weight: var(--tj-fw-medium);
}
.tj-product-details-description textarea {
  padding-top: 20px;
  height: 180px;
  line-height: 1.2;
  padding-bottom: 20px;
  resize: none;
}
.tj-product-details-description input[type=checkbox] {
  width: 17px;
  height: 17px;
  margin-top: 0 !important;
}
.tj-product-details-description .form-submit input[type=submit] {
  font-family: var(--tj-ff-body);
  font-weight: 600;
  font-size: 16px;
  line-height: 1;
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-dark);
  display: inline-block;
  padding: 20px 30px 20px 30px;
  border-radius: 50px;
}
.tj-product-details-description .form-submit input[type=submit]:focus, .tj-product-details-description .form-submit input[type=submit]:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
}
.tj-product-details-description .woocommerce-Reviews-title {
  font-family: var(--tj-ff-body);
  font-weight: 600;
  font-size: 24px;
  letter-spacing: -0.02em;
  margin-bottom: 25px;
}
.tj-product-details-description .woocommerce-review__author {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--tj-color-heading-primary);
  position: relative;
  text-transform: capitalize;
}
.tj-product-details-description .woocommerce-review__published-date {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: #888888;
}
.tj-product-details-description .comment-form-rating {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.tj-product-details-description .comment-form-rating label {
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-text-body);
}
.tj-product-details-description .comment-form-rating .stars > span {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-description .comment-form-rating .nice-select {
  display: none;
}
.tj-product-details-description .comment-form-rating select {
  display: none;
}
.tj-product-details-description .comment-form-rating span {
  -webkit-margin-end: 5px;
          margin-inline-end: 5px;
  font-size: 16px;
  color: var(--tj-color-text-body);
  font-weight: 500;
  margin-bottom: 5px;
}
.tj-product-details-description .comment-reply-title {
  font-family: var(--tj-ff-heading);
  font-weight: 600;
  font-size: 19px;
  letter-spacing: -0.02em;
  margin-bottom: 4px;
  color: var(--tj-color-heading-primary);
}
.tj-product-details-description .comment-form-comment label {
  font-family: var(--tj-ff-body);
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-text-body);
  display: inline-block;
  margin-bottom: 20px;
}
.tj-product-details-description .comment-form-author, .tj-product-details-description .comment-form-email {
  width: 48%;
  display: inline-block;
  -webkit-margin-end: 30px;
          margin-inline-end: 30px;
}
@media (max-width: 1200px) {
  .tj-product-details-description .comment-form-author, .tj-product-details-description .comment-form-email {
    width: 100%;
  }
}
.tj-product-details-description .comment-form-email {
  -webkit-margin-end: 0;
          margin-inline-end: 0;
  width: 49%;
}
@media (max-width: 1200px) {
  .tj-product-details-description .comment-form-email {
    width: 100%;
  }
}

.tj-product-details-description table tbody > tr:nth-child(odd) > td,
.tj-product-details-description table tbody > tr:nth-child(odd) > th {
  background-color: inherit;
}
.tj-product-details-description table {
  border: 1px solid var(--tj-color-border-2);
  width: 100%;
  margin-bottom: 0;
}
.tj-product-details-description table th, .tj-product-details-description table td {
  padding: 0;
  line-height: 1.5;
  vertical-align: middle;
  border: 1px solid var(--tj-color-border-2);
  text-align: start;
  background-color: transparent;
}
.tj-product-details-description table th p, .tj-product-details-description table td p {
  margin-bottom: 0;
}
.tj-product-details-description table tr:not(:last-child) {
  border-bottom: 1px solid var(--tj-color-border-2);
}
.tj-product-details-description table tr th {
  font-size: 16px;
  color: var(--tj-color-heading-primary);
  background-color: transparent !important;
  width: 306px;
  padding: 12px 34px;
  font-weight: 500;
}
@media (max-width: 575px) {
  .tj-product-details-description table tr th {
    width: 160px;
    padding: 12px 20px;
  }
}
.tj-product-details-description table tr td {
  padding: 12px 34px;
}
@media (max-width: 575px) {
  .tj-product-details-description table tr td {
    padding: 12px 20px;
  }
}
.tj-product-details-description table tr td:first-child {
  font-size: 14px;
  color: var(--tj-color-heading-primary);
  background-color: #f9f9f9;
  width: 306px;
}
@media (max-width: 575px) {
  .tj-product-details-description table tr td:first-child {
    width: 160px;
  }
}
.tj-product-details-description table tr td:last-child {
  font-size: 16px;
  color: var(--tj-color-text-body);
}
.tj-product-details-description table tr td p {
  margin-bottom: 0;
  font-size: 14px;
}

body .woocommerce-notices-wrapper,
body .woocommerce-message,
body .woocommerce-error,
body .woocommerce-info {
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  border-radius: 0;
}
body .woocommerce-notices-wrapper:focus, body .woocommerce-notices-wrapper:hover, body .woocommerce-notices-wrapper:active, body .woocommerce-notices-wrapper:visited, body .woocommerce-notices-wrapper:focus-visible,
body .woocommerce-message:focus,
body .woocommerce-message:hover,
body .woocommerce-message:active,
body .woocommerce-message:visited,
body .woocommerce-message:focus-visible,
body .woocommerce-error:focus,
body .woocommerce-error:hover,
body .woocommerce-error:active,
body .woocommerce-error:visited,
body .woocommerce-error:focus-visible,
body .woocommerce-info:focus,
body .woocommerce-info:hover,
body .woocommerce-info:active,
body .woocommerce-info:visited,
body .woocommerce-info:focus-visible {
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  border-radius: 0;
}

.woocommerce-message {
  background: var(--tj-color-theme-bg);
  padding: 15px 20px;
  margin-bottom: 30px;
  border: 1px solid var(--tj-color-border-2);
  color: var(--tj-color-theme-primary);
}
.woocommerce-message a {
  text-decoration: underline;
  color: var(--tj-color-theme-primary);
  font-weight: 500;
}
.woocommerce-message a:hover {
  color: var(--tj-color-theme-dark);
}

.product-type-variable .tj-product-details-wrapper .product-percentage-badges {
  -webkit-transform: translateY(0px);
      -ms-transform: translateY(0px);
          transform: translateY(0px);
}

.tj-product-details-wrapper .variations_form table,
.tj-product-details-action-wrapper .variations_form table {
  position: relative;
  margin-bottom: 30px;
}
.tj-product-details-wrapper .variations_form table td,
.tj-product-details-action-wrapper .variations_form table td {
  padding: 12px 12px;
}
.tj-product-details-wrapper .variations_form table tr .nice-select,
.tj-product-details-action-wrapper .variations_form table tr .nice-select {
  width: 100%;
  background-color: #f9f9f9;
  border: 1px solid rgba(1, 15, 28, 0.1);
  border-radius: 0;
  font-size: 14px;
  color: var(--tj-color-heading-primary);
  height: 40px;
  line-height: 38px;
  padding: 0 25px;
  min-width: 204px;
  float: none;
}
.tj-product-details-wrapper .variations_form table tr .nice-select::after,
.tj-product-details-action-wrapper .variations_form table tr .nice-select::after {
  position: absolute;
  content: "\f107";
  top: 50%;
  inset-inline-end: 15px;
  font-family: var(--tj-ff-fontawesome);
  color: var(--tj-color-heading-primary);
  font-weight: 500;
  pointer-events: none;
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  margin-top: 0;
  -webkit-transform-origin: center;
      -ms-transform-origin: center;
          transform-origin: center;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: auto;
  height: auto;
}
.tj-product-details-wrapper .variations_form table tr .nice-select.open::after,
.tj-product-details-action-wrapper .variations_form table tr .nice-select.open::after {
  -webkit-transform: translateY(-50%) rotate(-180deg);
  -ms-transform: translateY(-50%) rotate(-180deg);
  transform: translateY(-50%) rotate(-180deg);
}
.tj-product-details-wrapper .variations_form table tr .nice-select.open .list,
.tj-product-details-action-wrapper .variations_form table tr .nice-select.open .list {
  -webkit-transform: scale(1) translateY(0px);
  -ms-transform: scale(1) translateY(0px);
  transform: scale(1) translateY(0px);
}
.tj-product-details-wrapper .variations_form table tr .nice-select .list,
.tj-product-details-action-wrapper .variations_form table tr .nice-select .list {
  margin-top: 0;
  border-radius: 0;
  -webkit-transform-origin: center center;
      -ms-transform-origin: center center;
          transform-origin: center center;
  -webkit-transform: scale(0.9) translateY(0px);
  -ms-transform: scale(0.9) translateY(0px);
  transform: scale(0.9) translateY(0px);
  width: 100%;
  padding: 10px 0;
}
.tj-product-details-wrapper .variations_form table tr .nice-select .list .option,
.tj-product-details-action-wrapper .variations_form table tr .nice-select .list .option {
  line-height: 1.2;
  min-height: inherit;
  padding-top: 5px;
  padding-bottom: 5px;
}
.tj-product-details-wrapper .variations_form table tr .nice-select .list .option:hover,
.tj-product-details-action-wrapper .variations_form table tr .nice-select .list .option:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-wrapper .variations_form .disabled,
.tj-product-details-action-wrapper .variations_form .disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.tj-product-details-wrapper .variations_form .quantity,
.tj-product-details-action-wrapper .variations_form .quantity {
  position: relative;
  width: 120px;
}
.tj-product-details-wrapper .variations_form .reset_variations,
.tj-product-details-action-wrapper .variations_form .reset_variations {
  color: red;
  position: absolute;
  margin-top: 10px;
}
.tj-product-details-wrapper .reset_variations,
.tj-product-details-action-wrapper .reset_variations {
  display: none;
}
.tj-product-details-wrapper .single_variation_wrap .single_variation,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation {
  margin-bottom: 30px;
}
.tj-product-details-wrapper .single_variation_wrap .single_variation .amount,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation .amount {
  font-weight: 600;
  font-size: 24px;
  color: var(--tj-color-heading-primary);
  -webkit-margin-start: 1px;
          margin-inline-start: 1px;
}
.tj-product-details-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price span,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price span {
  font-weight: 500;
  font-size: 24px;
  letter-spacing: -0.02em;
  color: var(--tj-color-heading-primary);
}
.tj-product-details-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price del,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price del {
  text-decoration: none;
}
.tj-product-details-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price del span,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price del span {
  font-weight: 400;
  font-size: 16px;
  text-decoration-line: line-through;
  color: #767a7d;
}
.tj-product-details-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price ins,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price ins {
  text-decoration: none;
}
.tj-product-details-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price ins span,
.tj-product-details-action-wrapper .single_variation_wrap .single_variation .woocommerce-variation-price ins span {
  color: var(--tj-color-heading-primary);
}

.woosw-popup {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.woosc-quick-table {
  margin-bottom: 60px;
}
.woosc-quick-table h2 {
  margin-bottom: 25px;
}
.woosc-quick-table table thead th {
  padding: 15px;
}
.woosc-quick-table table tbody tr td {
  z-index: 1;
}
.woosc-quick-table table tbody tr td .add_to_cart_button, .woosc-quick-table table tbody tr td .product_type_grouped, .woosc-quick-table table tbody tr td .product-action-btn {
  font-size: 14px;
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  width: 100%;
  padding: 5px 15px;
  display: inline-block;
  text-align: center;
  font-weight: 500;
}
.woosc-quick-table table tbody tr td .add_to_cart_button svg, .woosc-quick-table table tbody tr td .product_type_grouped svg, .woosc-quick-table table tbody tr td .product-action-btn svg {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
}
.woosc-quick-table table tbody tr td .add_to_cart_button svg, .woosc-quick-table table tbody tr td .add_to_cart_button i, .woosc-quick-table table tbody tr td .product_type_grouped svg, .woosc-quick-table table tbody tr td .product_type_grouped i, .woosc-quick-table table tbody tr td .product-action-btn svg, .woosc-quick-table table tbody tr td .product-action-btn i {
  -webkit-margin-end: 4px;
          margin-inline-end: 4px;
}
.woosc-quick-table table tbody tr td .add_to_cart_button:hover, .woosc-quick-table table tbody tr td .product_type_grouped:hover, .woosc-quick-table table tbody tr td .product-action-btn:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
}
.woosc-quick-table table tbody tr td p {
  margin-bottom: 0;
}
.woosc-quick-table table tbody tr td span {
  font-weight: 500;
  font-size: 11px;
  display: inline-block;
}
.woosc-quick-table table tbody tr td del {
  font-family: var(--tj-ff-body);
  font-weight: 500;
  font-size: 16px;
  color: #c2c2d3;
}
.woosc-quick-table table tbody tr td del .woocommerce-Price-amount, .woosc-quick-table table tbody tr td del span {
  font-size: 13px;
  color: #c2c2d3;
}
.woosc-quick-table table tbody tr td ins {
  text-decoration: none;
  font-family: var(--tj-ff-body);
  font-weight: 500;
}
.woosc-quick-table table tbody tr td ins .woocommerce-Price-amount {
  font-size: 16px;
  color: var(--tj-color-heading-primary);
}
.woosc-quick-table table tbody tr td ins span {
  color: var(--tj-color-heading-primary);
}
.woosc-quick-table table tbody tr td:first-child {
  -webkit-padding-start: 30px;
          padding-inline-start: 30px;
}
.woosc-quick-table table tbody tr:not(:first-child) td {
  padding: 15px 10px;
}
.woosc-quick-table table tbody tr:nth-child(2n) {
  background-color: var(--tj-color-theme-bg6);
}

.tj-product-details-wrapper .grouped_form .tj-product-details-quantity {
  margin-bottom: 0;
}
.tj-product-details-wrapper .grouped_form tbody tr td {
  border: 1px solid #e7e8eb;
}
.tj-product-details-wrapper .grouped_form tbody tr td:first-child {
  padding: 0px 15px;
}
.tj-product-details-wrapper .grouped_form tbody tr td:not(:first-child) {
  padding: 15px 15px;
}
.tj-product-details-wrapper .grouped_form tbody tr td span {
  font-family: var(--tj-ff-body);
  font-weight: 600;
  font-size: 16px;
  color: var(--tj-color-text-body);
}
.tj-product-details-wrapper .grouped_form tbody tr td del {
  font-family: var(--tj-ff-body);
  font-weight: 600;
  font-size: 16px;
  color: #c2c2d3;
}
.tj-product-details-wrapper .grouped_form tbody tr td del .woocommerce-Price-amount, .tj-product-details-wrapper .grouped_form tbody tr td del span {
  font-size: 16px;
  color: #c2c2d3;
}
.tj-product-details-wrapper .grouped_form tbody tr td ins {
  text-decoration: none;
  font-family: var(--tj-ff-body);
  font-weight: 500;
}
.tj-product-details-wrapper .grouped_form tbody tr td ins .woocommerce-Price-amount {
  font-size: 16px;
  color: var(--tj-color-heading-primary);
}
.tj-product-details-wrapper .grouped_form tbody tr td ins span {
  color: var(--tj-color-heading-primary);
}
.tj-product-details-wrapper .grouped_form .woocommerce-grouped-product-list {
  margin-bottom: 35px;
}
.tj-product-details-wrapper .grouped_form .woocommerce-grouped-product-list-item__label a {
  color: var(--tj-color-heading-primary);
  font-weight: 600;
}
.tj-product-details-wrapper .grouped_form .woocommerce-grouped-product-list-item__label a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-wrapper .grouped_form .single_add_to_cart_button {
  padding: 12px 30px;
}

.woosw-item--time {
  display: none !important;
}

.woosw-item--price span {
  font-family: var(--tj-ff-body);
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-text-body);
}
.woosw-item--price del {
  font-family: var(--tj-ff-body);
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-text-body-2);
  text-decoration: none;
}
.woosw-item--price del .woocommerce-Price-amount, .woosw-item--price del span {
  font-size: 16px;
  color: var(--tj-color-text-body-2);
  text-decoration: line-through;
}
.woosw-item--price ins {
  text-decoration: none;
  font-family: var(--tj-ff-body);
  font-weight: 500;
}
.woosw-item--price ins .woocommerce-Price-amount {
  font-size: 16px;
  color: var(--tj-color-heading-primary);
}
.woosw-item--price ins span {
  color: var(--tj-color-heading-primary);
}

.woosw-item--add .add_to_cart_button, .woosw-item--add .added_to_cart, .woosw-item--add .product-action {
  font-weight: 600;
  color: var(--tj-color-heading-primary);
  background-color: #fff;
  display: inline-block;
  text-align: center;
  font-size: 0;
  border: 1px solid #e7e8eb;
  width: 50px;
  height: 50px;
  line-height: 55px;
}
.woosw-item--add .add_to_cart_button.added_to_cart, .woosw-item--add .added_to_cart.added_to_cart, .woosw-item--add .product-action.added_to_cart {
  line-height: 50px;
}
.woosw-item--add .add_to_cart_button:hover, .woosw-item--add .added_to_cart:hover, .woosw-item--add .product-action:hover {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.woosw-item--add .add_to_cart_button svg, .woosw-item--add .add_to_cart_button i, .woosw-item--add .added_to_cart svg, .woosw-item--add .added_to_cart i, .woosw-item--add .product-action svg, .woosw-item--add .product-action i {
  font-size: 14px;
  -webkit-margin-end: 0;
          margin-inline-end: 0;
}
.woosw-item--add .add_to_cart_button svg, .woosw-item--add .added_to_cart svg, .woosw-item--add .product-action svg {
  -webkit-transform: translate(0px, -3px);
  -ms-transform: translate(0px, -3px);
  transform: translate(0px, -3px);
}
.woosw-item--add .add_to_cart_button .product-action-tooltip, .woosw-item--add .added_to_cart .product-action-tooltip, .woosw-item--add .product-action .product-action-tooltip {
  display: none;
}
.woosw-item--add .woocommerce a.added_to_cart {
  font-size: 14px;
}
.woosw-item--add .woocommerce a.added_to_cart::before {
  color: var(--tj-color-heading-primary);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.woosw-item--add .woocommerce a.added_to_cart:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
  border-color: var(--tj-color-heading-primary);
}
.woosw-item--add .woocommerce a.added_to_cart:hover::before {
  color: var(--tj-color-common-white);
}
.woosw-item--add .woocommerce a.button.loading::before {
  color: black;
}
.woosw-item--add .woocommerce a.button.loading:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
  border-color: var(--tj-color-heading-primary);
}
.woosw-item--add .woocommerce a.button.loading:hover::before {
  color: var(--tj-color-heading-primary);
}

.woosw-item--name a {
  color: var(--tj-color-heading-primary);
}
.woosw-item--name a:hover {
  color: var(--tj-color-theme-primary);
}

.woosw-popup-content-bot .woosw-popup-content-bot-inner a {
  color: var(--tj-color-heading-primary);
}
.woosw-popup-content-bot .woosw-popup-content-bot-inner a:hover {
  color: var(--tj-color-theme-primary) !important;
  border-color: var(--tj-color-theme-primary) !important;
}

.woosw-popup.woosw-popup-right .woosw-popup-inner .woosw-popup-content .woosw-popup-content-top {
  background-color: var(--tj-color-common-white);
  border-bottom: 1px solid #e7e8eb;
}

.woosw-popup .woosw-popup-inner .woosw-popup-content .woosw-popup-content-top .woosw-popup-close:hover {
  color: var(--tj-color-theme-primary);
}

.product_meta {
  padding-top: 34px;
  border-top: 1px solid #dadce0;
  margin-top: 32px;
}

/* cart page css */
div.woocommerce .woocommerce-cart-form table {
  background: var(--tj-color-common-white);
  border-color: var(--tj-color-border-2);
  border-radius: 0;
  border-style: solid;
  border-width: 1px 0 0 1px;
  text-align: center;
  width: 100%;
  margin-bottom: 0;
}
div.woocommerce .woocommerce-cart-form table thead {
  border-bottom: 1px solid var(--tj-color-border-2);
  font-weight: 600;
}
div.woocommerce .woocommerce-cart-form table thead th {
  -webkit-border-end: 1px solid var(--tj-color-border-2);
          border-inline-end: 1px solid var(--tj-color-border-2);
  text-align: start;
  font-family: var(--tj-ff-body);
  font-weight: 600;
  font-size: 16px;
  color: var(--tj-color-heading-primary);
  padding: 5px;
  -webkit-padding-start: 20px;
          padding-inline-start: 20px;
}
div.woocommerce .woocommerce-cart-form table thead th:first-child {
  -webkit-border-end: 1px solid transparent;
          border-inline-end: 1px solid transparent;
}
div.woocommerce .woocommerce-cart-form table thead th.product-subtotal {
  -webkit-border-end: 1px solid transparent;
          border-inline-end: 1px solid transparent;
}
div.woocommerce .woocommerce-cart-form table tbody tr:not(:last-child) {
  border-bottom: 1px solid var(--tj-color-border-2);
}
div.woocommerce .woocommerce-cart-form table tbody tr td:first-child {
  -webkit-border-end: 1px solid transparent;
          border-inline-end: 1px solid transparent;
}
div.woocommerce .woocommerce-cart-form table tbody tr td {
  border: 1px solid var(--tj-color-border-2);
  text-align: start;
  padding: 12px;
  -webkit-padding-start: 20px;
          padding-inline-start: 20px;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-thumbnail {
  width: 125px;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-thumbnail img {
  width: 100%;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-name {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-name h5 {
  margin: 0;
  text-align: start;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  div.woocommerce .woocommerce-cart-form table tbody tr td.product-name h5 {
    font-size: 16px;
  }
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-name a {
  text-decoration: none;
  color: var(--tj-color-heading-primary);
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-name a:hover {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-price span {
  font-weight: 400;
  font-size: 16px;
  color: var(--tj-color-text-body);
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-subtotal {
  -webkit-border-end: 1px solid transparent;
          border-inline-end: 1px solid transparent;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-subtotal span {
  font-weight: 400;
  font-size: 16px;
  color: var(--tj-color-text-body);
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  border-radius: 0;
  position: relative;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .quantity {
  position: relative;
  background: transparent;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border: 1px solid var(--tj-color-border-2);
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .tj-cart-plus, div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .tj-cart-minus {
  display: inline-block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .tj-cart-plus:hover, div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .tj-cart-minus:hover {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .tj-cart-input[type=text] {
  height: 44px;
  line-height: 44px;
  width: 45px;
  background-color: transparent;
  border: 0;
  -webkit-border-end: 1px solid var(--tj-color-border-2);
          border-inline-end: 1px solid var(--tj-color-border-2);
  -webkit-border-start: 1px solid var(--tj-color-border-2);
          border-inline-start: 1px solid var(--tj-color-border-2);
  border-radius: 0px;
  font-size: 16px;
  color: var(--tj-color-theme-dark);
  text-align: center;
  margin: 0;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-quantity .tj-product-quantity .tj-cart-input[type=text]:focus {
  outline: none;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-remove {
  text-align: center;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-remove a {
  color: var(--tj-color-heading-primary);
  font-size: 22px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid var(--tj-color-border-2);
  text-decoration: none;
}
div.woocommerce .woocommerce-cart-form table tbody tr td.product-remove a:is(:hover, :focus) {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-cart-form .actions {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  padding-top: 35px;
}
div.woocommerce .woocommerce-cart-form .actions .coupon label {
  display: block;
  font-size: 14px;
  color: var(--tj-color-heading-primary);
  font-weight: 500;
  margin-bottom: 7px;
}
div.woocommerce .woocommerce-cart-form .actions .coupon #coupon_code {
  padding: 0px 20px;
  width: 225px;
  height: 56px;
  line-height: 56px;
  border: 1px solid var(--tj-color-border-2);
  outline: 0;
  font-weight: 400;
  font-size: 16px;
  color: var(--tj-color-text-body);
  -webkit-box-shadow: none;
          box-shadow: none;
  background-color: transparent;
  border-radius: 0;
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
  -webkit-transition: all 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.3, 0, 0, 0.3);
}
@media (max-width: 575px) {
  div.woocommerce .woocommerce-cart-form .actions .coupon #coupon_code {
    width: 100%;
    -webkit-margin-end: 0;
            margin-inline-end: 0;
    margin-bottom: 20px;
  }
}
div.woocommerce .woocommerce-cart-form .actions .coupon #coupon_code:focus {
  border-color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-cart-form .actions button[name=update_cart]:disabled {
  cursor: not-allowed;
  opacity: 0.2;
}
div.woocommerce .select2-container .select2-selection--single:focus {
  background-color: var(--tj-color-common-white);
}
div.woocommerce .select2-container .select2-selection--single:focus .select2-selection--single {
  background-color: var(--tj-color-common-white);
  border-color: var(--tj-color-theme-primary);
}
div.woocommerce .select2-container .select2-selection__arrow {
  height: 46px;
  inset-inline-end: 25px;
}
div.woocommerce .cart_totals > h3 {
  margin-bottom: 20px;
}
div.woocommerce .cart_totals table {
  width: 100%;
  border: 0;
}
div.woocommerce .cart_totals table tbody tr {
  border: 1px solid var(--tj-color-border-2);
}
div.woocommerce .cart_totals table tbody tr th {
  border: 0;
  text-align: start;
  font-weight: 600;
  font-size: 16px;
  font-family: var(--tj-ff-heading);
  color: var(--tj-color-heading-primary);
  padding: 15px 20px;
  width: 260px;
}
div.woocommerce .cart_totals table tbody tr td {
  border: 0;
  text-align: start;
  font-size: 16px;
  font-weight: 500;
  color: var(--tj-color-heading-primary);
  padding: 15px 20px;
}
div.woocommerce .cart_totals table tbody tr td[data-title=Shipping] {
  text-align: start;
  line-height: 1.3;
  -webkit-padding-start: 15px;
          padding-inline-start: 15px;
}
div.woocommerce .cart_totals table tbody tr td[data-title=Total] {
  font-size: 16px;
  font-weight: 400;
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .shipping-calculator-button {
  margin-top: 7px;
  display: inline-block;
  color: var(--tj-color-theme-primary);
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .shipping-calculator-button:hover {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .shipping-calculator-form {
  margin-top: 14px;
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator input {
  border-width: 1px;
  height: 46px;
  border: 1px solid var(--tj-color-border-2);
  padding: 0 10px;
  width: 100%;
  font-size: 16px;
  background-color: transparent;
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator input:focus {
  border-color: var(--tj-color-theme-primary);
  background-color: var(--tj-color-common-white);
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .select2.select2-container {
  width: 215px !important;
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select {
  min-width: 100%;
  font-family: var(--tj-ff-body);
  color: var(--tj-color-heading-primary);
  font-size: 14px;
  border: 0;
  -webkit-padding-start: 10px;
          padding-inline-start: 10px;
  -webkit-padding-end: 50px;
          padding-inline-end: 50px;
  font-weight: 500;
  border-radius: 0;
  border: 1px solid var(--tj-color-border-2);
  float: none;
  height: 46px;
  line-height: 44px;
  background-color: var(--tj-color-theme-bg);
  color: var(--tj-color-heading-primary);
  width: 100%;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select {
    width: 100%;
    border-radius: 40px;
    border: 1px solid var(--tj-color-border-2);
    height: 50px;
    line-height: 50px;
  }
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select::after {
  inset-inline-end: 25px;
  position: absolute;
  top: 50%;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select::after {
    inset-inline-end: 25px;
  }
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select .list {
  margin-top: 0;
  border-radius: 0;
  width: 100%;
  padding-bottom: 10px;
  padding-top: 9px;
}
@media (max-width: 575px) {
  div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select .list {
    margin-top: 0;
  }
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select .list .option {
  line-height: 29px;
  min-height: 29px;
  -webkit-padding-start: 18px;
          padding-inline-start: 18px;
  -webkit-padding-end: 18px;
          padding-inline-end: 18px;
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select .list .option:hover {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select .list .option.selected {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .cart_totals table tbody tr td .woocommerce-shipping-calculator .nice-select:focus {
  border-color: var(--tj-color-theme-primary);
  background-color: var(--tj-color-common-white);
}
div.woocommerce .cart_totals table tbody tr.order-total td {
  color: var(--tj-color-heading-primary);
}
div.woocommerce .woocommerce-billing-fields__field-wrapper .select2-selection--single, div.woocommerce .woocommerce-shipping-fields__field-wrapper .select2-selection--single {
  height: 48px;
  line-height: 42px;
  border: 1px solid var(--tj-color-theme-dark);
  background-color: transparent;
  border-radius: 0;
}
div.woocommerce .woocommerce-billing-fields__field-wrapper .select2-selection--single:focus, div.woocommerce .woocommerce-shipping-fields__field-wrapper .select2-selection--single:focus {
  background-color: var(--tj-color-common-white);
}
div.woocommerce .woocommerce-billing-fields__field-wrapper .select2-selection--single:focus .select2-selection--single, div.woocommerce .woocommerce-shipping-fields__field-wrapper .select2-selection--single:focus .select2-selection--single {
  background-color: var(--tj-color-common-white);
  border-color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-billing-fields__field-wrapper .select2-selection__arrow, div.woocommerce .woocommerce-shipping-fields__field-wrapper .select2-selection__arrow {
  height: 46px;
  inset-inline-end: 25px;
}
div.woocommerce .woocommerce-shipping-totals .woocommerce-shipping-methods {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
div.woocommerce .woocommerce-shipping-totals .woocommerce-shipping-methods li {
  list-style: none;
}
div.woocommerce .woocommerce-shipping-totals .woocommerce-shipping-methods li label {
  -webkit-margin-start: 5px;
          margin-inline-start: 5px;
}
div.woocommerce .woocommerce-shipping-totals .woocommerce-shipping-methods li label:hover {
  cursor: pointer;
}

/* checkout css start */
.woocommerce-checkout .woocommerce-form-coupon-toggle {
  position: relative;
}
.woocommerce-checkout .woocommerce-form-coupon-toggle .showcoupon {
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  font-weight: 500;
  color: var(--tj-color-theme-primary);
}
.woocommerce-checkout .woocommerce-form-coupon-toggle .woocommerce-info {
  color: var(--tj-color-theme-primary);
  background-color: var(--tj-color-theme-bg);
  border: 1px solid var(--tj-color-border-2);
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 0;
  padding: 20px 30px;
  position: relative;
  width: 100%;
}
.woocommerce-checkout .checkout_coupon p {
  color: var(--tj-color-heading-primary);
  margin-bottom: 0;
}
.woocommerce-checkout .checkout_coupon p:first-child {
  margin-bottom: 10px;
  color: var(--tj-color-text-body);
  font-weight: 500;
}
.woocommerce-checkout .checkout_coupon #coupon_code {
  width: 350px;
  height: 56px;
  line-height: 56px;
  border: 1px solid var(--tj-color-theme-dark);
  border-radius: 0;
  background-color: transparent;
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
}
.woocommerce-checkout .checkout_coupon #coupon_code:focus {
  background-color: var(--tj-color-theme-bg);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .woocommerce-checkout .checkout_coupon #coupon_code {
    margin: 0;
  }
}
.woocommerce-checkout #customer_form_details {
  border: 0;
  border-radius: 0;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields h3 {
  margin-bottom: 20px;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row > label {
  line-height: 1;
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  color: var(--tj-color-text-body);
  font-size: 16px;
  margin-bottom: 15px;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea {
  background: transparent;
  border: 1px solid var(--tj-color-theme-dark);
  border-radius: 0;
  height: 48px;
  padding: 5px 22px;
  width: 100%;
  outline: none;
  -webkit-box-shadow: none;
  -ms-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input::-webkit-input-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea::-webkit-input-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input::-moz-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea::-moz-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input:-ms-input-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea:-ms-input-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input::-ms-input-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea::-ms-input-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input::placeholder, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea::placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row input:focus, .woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea:focus {
  background-color: var(--tj-color-theme-bg);
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .form-row textarea {
  padding-top: 15px;
  padding-bottom: 15px;
  resize: none;
  line-height: 1.2;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .select2-container .select2-results__options .select2-results__option {
  font-weight: 400;
  -webkit-padding-start: 18px;
          padding-inline-start: 18px;
  -webkit-padding-end: 29px;
          padding-inline-end: 29px;
  text-align: start;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .select2-container .select2-selection--single {
  border: 1px solid var(--tj-color-theme-dark);
  margin-bottom: 45px;
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .select2-container .select2-selection__rendered {
  height: 48px;
  line-height: 42px;
  padding: 0 22px;
  width: 100%;
  color: var(--tj-color-text-body);
}
.woocommerce-checkout #customer_form_details .woocommerce-billing-fields__field-wrapper .woocommerce-form__input[type=checkbox] {
  width: 20px;
  height: 20px;
  border: 1px solid var(--tj-color-border-2);
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row.notes {
  margin-bottom: 0;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row > label {
  margin-top: -6px;
  margin-bottom: 10px;
  -webkit-margin-start: 0;
          margin-inline-start: 0;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input {
  background: var(--tj-color-common-white);
  border: 1px solid var(--tj-color-theme-dark);
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input:focus {
  border-color: var(--tj-color-theme-primary);
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea {
  border: 1px solid var(--tj-color-theme-dark);
  height: 48px;
  padding: 5px 22px;
  width: 100%;
  outline: 0;
  font-size: 16px;
  border-radius: 0;
  background-color: transparent;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input::-webkit-input-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea::-webkit-input-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input::-moz-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea::-moz-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input:-ms-input-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea:-ms-input-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input::-ms-input-placeholder, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea::-ms-input-placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input::placeholder, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea::placeholder {
  color: #6f7172;
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row input:focus, .woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea:focus {
  background-color: var(--tj-color-theme-bg);
}
.woocommerce-checkout #customer_form_details .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper .form-row textarea {
  line-height: 1.3;
  resize: none;
  padding-top: 15px;
  padding-bottom: 15px;
  height: 150px;
}
.woocommerce-checkout .woocommerce-form-login-toggle .woocommerce-info {
  margin-bottom: 15px;
  display: block;
  border: 1px dashed #aab0b2;
  font-weight: 400;
  font-size: 14px;
  color: var(--tj-color-heading-primary);
  padding: 8px 25px;
}
.woocommerce-checkout .woocommerce-form-login-toggle .woocommerce-info a {
  color: var(--tj-color-theme-primary);
  text-decoration: underline;
}
.woocommerce-checkout .woocommerce-form-login {
  margin-bottom: 30px;
}
.woocommerce-checkout .woocommerce-form-login p {
  font-size: 14px;
  color: var(--tj-color-heading-primary);
  margin-bottom: 0;
}
.woocommerce-checkout .woocommerce-form-login p:first-child {
  margin-bottom: 15px;
}
.woocommerce-checkout .woocommerce-form-login label {
  margin: 0;
  display: block;
  height: auto;
}
.woocommerce-checkout .woocommerce-form-login label.woocommerce-form-login__rememberme input {
  border: 1px solid #e7e8eb;
  background-color: transparent;
  -webkit-margin-end: 5px;
          margin-inline-end: 5px;
  height: auto;
}
.woocommerce-checkout .woocommerce-form-login label.woocommerce-form-login__rememberme:hover {
  cursor: pointer;
}
.woocommerce-checkout .woocommerce-form-login .lost_password {
  margin-top: 15px;
  padding-bottom: 15px;
}
.woocommerce-checkout .woocommerce-form-login .lost_password a:hover {
  color: var(--tj-color-theme-primary);
}
.woocommerce-checkout .woocommerce-form-login input {
  height: 54px;
  background-color: var(--tj-color-common-white);
  border-color: var(--tj-color-common-white);
  margin-bottom: 15px;
  border-width: 1px;
}
.woocommerce-checkout .woocommerce-form-login input:focus {
  border-color: var(--tj-color-theme-primary);
}
.woocommerce-checkout .woocommerce-form-login .tj-btn {
  padding: 14px 30px;
}
.woocommerce-checkout .woocommerce-form-login .tj-btn:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
}
.woocommerce-checkout .woocommerce-form-login .woocommerce-button {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: var(--tj-color-common-white);
  background: var(--tj-color-theme-primary);
  text-align: center;
  font-family: var(--tj-ff-body);
  padding: 14px 30px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  letter-spacing: -0.02em;
}
.woocommerce-checkout .woocommerce-form-login .woocommerce-button:hover {
  background-color: var(--tj-color-heading-primary);
}
.woocommerce-checkout .tj-free-progress-bar {
  background-color: var(--tj-color-common-white);
}
.woocommerce-checkout #ship-to-different-address {
  font-weight: 600;
  font-size: 26px;
  margin-bottom: 35px;
}

/* wishlist css start */
.woosw-list table.woosw-items {
  width: 100%;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items {
    border: 1px solid var(--tj-color-border-2);
  }
}
.woosw-list table.woosw-items tr.woosw-item:nth-child(2n) td {
  background: transparent;
}
.woosw-list table.woosw-items tr.woosw-item:hover td {
  background: transparent;
}
.woosw-list table.woosw-items tr.woosw-item td {
  padding: 30px;
  text-align: start;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item td {
    padding: 15px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item td {
    display: block;
    width: 100%;
    text-align: center;
    border: 0;
    border-bottom: 1px solid var(--tj-color-border-2);
  }
}
.woosw-list table.woosw-items tr.woosw-item td.woosw-item--info {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
.woosw-list table.woosw-items tr.woosw-item td.woosw-item--image {
  -webkit-padding-end: 15px;
          padding-inline-end: 15px;
}
.woosw-list table.woosw-items tr.woosw-item td a.wc-forward,
.woosw-list table.woosw-items tr.woosw-item td .tj-cart-btn {
  width: auto;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-decoration: none;
}
.woosw-list table.woosw-items tr.woosw-item td .woosw-item--stock,
.woosw-list table.woosw-items tr.woosw-item td .woosw-item--atc {
  display: inline-block;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item td .woosw-item--stock,
  .woosw-list table.woosw-items tr.woosw-item td .woosw-item--atc {
    display: block;
  }
}
.woosw-list table.woosw-items tr.woosw-item td .woosw-item--atc {
  float: right;
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item td .woosw-item--atc {
    float: unset;
    margin: 0;
    margin-top: 10px;
  }
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--name a {
  font-size: 20px;
  font-family: var(--tj-ff-heading);
  line-height: 1.2;
  letter-spacing: -0.025em;
  font-weight: 600 !important;
  text-decoration: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item .woosw-item--name a {
    font-size: 16px;
  }
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--image {
  width: 150px;
  -webkit-border-end: 1px solid transparent;
          border-inline-end: 1px solid transparent;
  border-radius: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item .woosw-item--image {
    width: 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list table.woosw-items tr.woosw-item .woosw-item--image {
    width: 100%;
  }
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--image img {
  width: 100px;
  border-radius: 0;
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--remove span,
.woosw-list table.woosw-items tr.woosw-item .woosw-item--add span {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  cursor: pointer;
  border: 1px solid var(--tj-color-border-2);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  color: var(--tj-color-heading-primary);
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--remove span::before,
.woosw-list table.woosw-items tr.woosw-item .woosw-item--add span::before {
  color: var(--tj-color-heading-primary);
  font-size: 22px;
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--remove span:hover,
.woosw-list table.woosw-items tr.woosw-item .woosw-item--add span:hover {
  border-color: var(--tj-color-theme-primary);
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.woosw-list table.woosw-items tr.woosw-item .woosw-item--remove span:hover::before,
.woosw-list table.woosw-items tr.woosw-item .woosw-item--add span:hover::before {
  color: var(--tj-color-common-white);
}
.woosw-list .woosw-actions {
  display: none;
}

/* product details */
.single-product .tj-shop-area .related.products h2 {
  margin-bottom: 25px;
}

.tj-login-wrapper .tj-btn {
  background-color: var(--tj-color-heading-primary);
}
.tj-login-wrapper .tj-btn:hover {
  background-color: var(--tj-color-theme-primary);
}

/* order details css start */
.woocommerce-order .woocommerce-notice--success.woocommerce-thankyou-order-received {
  font-size: 24px;
  font-weight: 600;
  color: var(--tj-color-heading-primary);
  border: 2px dashed var(--tj-color-theme-primary);
  padding: 35px 30px;
  text-align: center;
  margin-bottom: 25px;
}
.woocommerce-order .woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: 20px 20px 0;
  -webkit-box-shadow: 1px 6px 19px rgba(3, 4, 28, 0.1);
          box-shadow: 1px 6px 19px rgba(3, 4, 28, 0.1);
  margin-bottom: 55px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woocommerce-order .woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: start;
  }
}
.woocommerce-order .woocommerce-order-overview.woocommerce-thankyou-order-details.order_details li {
  list-style: none;
  display: inline-block;
  margin-bottom: 20px;
  -webkit-margin-end: 30px;
          margin-inline-end: 30px;
}
.woocommerce-order .woocommerce-order-overview.woocommerce-thankyou-order-details.order_details li strong {
  display: block;
}

.woocommerce-order-received .tj-page-area {
  background-color: transparent;
}

.tj-order-details-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-order-details-wrapper {
    display: block;
  }
}
.tj-order-details-wrapper .woocommerce-order-details {
  -webkit-box-flex: 50%;
      -ms-flex: 50%;
          flex: 50%;
  -webkit-margin-end: 50px;
          margin-inline-end: 50px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-order-details-wrapper .woocommerce-order-details {
    -webkit-box-flex: 100%;
        -ms-flex: 100%;
            flex: 100%;
    -webkit-margin-end: 0;
            margin-inline-end: 0;
    margin-bottom: 50px;
  }
}
.tj-order-details-wrapper .woocommerce-order-details .woocommerce-order-details__title {
  margin-bottom: 15px;
  font-size: 22px;
}
.tj-order-details-wrapper .woocommerce-customer-details {
  -webkit-box-flex: 50%;
      -ms-flex: 50%;
          flex: 50%;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-order-details-wrapper .woocommerce-customer-details {
    -webkit-box-flex: 100%;
        -ms-flex: 100%;
            flex: 100%;
  }
}
.tj-order-details-wrapper .woocommerce-customer-details [class*=col-] {
  width: inherit;
}
.tj-order-details-wrapper .woocommerce-customer-details .woocommerce-column__title {
  margin-bottom: 15px;
  font-size: 22px;
}
.tj-order-details-wrapper .woocommerce-customer-details .woocommerce-column--billing-address {
  margin-bottom: 35px;
}
.tj-order-details-wrapper .shop_table {
  width: 100%;
}
.tj-order-details-wrapper .shop_table thead tr th {
  text-align: start;
  padding: 8px 12px;
  color: var(--tj-color-heading-primary);
  font-weight: 700;
  border-color: var(--tj-color-border-2);
}
.tj-order-details-wrapper .shop_table tbody tr td {
  padding: 8px 12px;
  border-color: var(--tj-color-border-2);
}
.tj-order-details-wrapper .shop_table tbody tr td.product-name {
  text-align: start;
}
.tj-order-details-wrapper .shop_table tbody tr td.product-name a {
  color: var(--tj-color-heading-primary);
}
.tj-order-details-wrapper .shop_table tbody tr td.product-total {
  text-align: start;
}
.tj-order-details-wrapper .shop_table tfoot tr th {
  padding: 8px 12px;
  text-align: start;
  font-weight: 700;
  color: var(--tj-color-heading-primary);
  border-color: var(--tj-color-border-2);
}
.tj-order-details-wrapper .shop_table tfoot tr td {
  border-color: var(--tj-color-border-2);
  padding: 8px 12px;
  text-align: start;
}

.tj-empty-cart {
  text-align: center;
  margin-bottom: 35px;
}
.tj-empty-cart img {
  max-width: 450px;
}

.cart-empty.woocommerce-info {
  font-family: var(--tj-ff-heading);
  font-size: 32px;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.025em;
  margin-bottom: 35px;
  text-align: center;
}

/* my account css */
div.woocommerce .woocommerce-MyAccount-navigation ul {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  border-bottom: 1px solid var(--tj-color-border-2);
}
div.woocommerce .woocommerce-MyAccount-navigation ul li {
  list-style: none;
  display: inline-block;
  margin-bottom: 0;
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
}
div.woocommerce .woocommerce-MyAccount-navigation ul li.is-active a::after {
  width: 100%;
  inset-inline-start: 0;
  inset-inline-end: auto;
}
div.woocommerce .woocommerce-MyAccount-navigation ul li a {
  font-size: 16px;
  padding: 8px 0;
  display: inline-block;
  position: relative;
  text-decoration: none;
}
div.woocommerce .woocommerce-MyAccount-navigation ul li a:hover {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-MyAccount-navigation ul li a::after {
  position: absolute;
  content: "";
  inset-inline-start: auto;
  inset-inline-end: 0;
  height: 2px;
  width: 0;
  bottom: 0;
  background-color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-info a.wc-forward {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  margin: unset;
  margin-top: 10px;
  text-decoration: none;
}
div.woocommerce .woocommerce-MyAccount-content > p a {
  color: var(--tj-color-theme-primary);
}
div.woocommerce .woocommerce-MyAccount-content .my_account_orders {
  width: 100%;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-order-downloads .shop_table {
  width: 100%;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-Addresses.u-columns {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: 15px;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address {
  width: 50%;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address .woocommerce-Address-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address .woocommerce-Address-title h3 {
  font-size: 28px;
  line-height: 1;
  font-weight: 500;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address .woocommerce-Address-title > a {
  color: var(--tj-color-theme-primary);
  font-weight: 500;
  font-size: 16px;
  -webkit-margin-start: 40px;
          margin-inline-start: 40px;
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-MyAccount-orders th {
  font-weight: 700;
}
div.woocommerce .woocommerce-MyAccount-content .edit-account fieldset {
  margin-top: 35px;
}
div.woocommerce .woocommerce-MyAccount-content .edit-account legend {
  font-size: 22px;
  color: var(--tj-color-heading-primary);
  font-weight: 500;
}
div.woocommerce .woocommerce-MyAccount-content .edit-account label {
  color: var(--tj-color-heading-primary);
}
div.woocommerce .woocommerce-MyAccount-content .edit-account input {
  height: 46px;
  line-height: 46px;
}
div.woocommerce .woocommerce-MyAccount-content .edit-account .tj-btn:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
}
div.woocommerce .woocommerce-MyAccount-content .woocommerce-address-fields__field-wrapper input {
  height: 46px;
  line-height: 46px;
}
div.woocommerce .woocommerce-MyAccount-content .tj-btn:hover {
  background-color: var(--tj-color-heading-primary);
}

/* tp progress bar */
.tj-free-progress-bar {
  padding: 20px 30px 30px;
  border: 1px solid #e7e8eb;
  width: 100%;
  margin-bottom: 30px;
}
.tj-free-progress-bar .free-shipping-notice {
  color: var(--tj-color-heading-primary);
  font-size: 13px;
  margin-bottom: 5px;
  font-weight: 500;
}
.tj-free-progress-bar .free-shipping-notice a {
  color: var(--tj-color-theme-primary);
}
.tj-free-progress-bar .free-shipping-notice span {
  color: var(--tj-color-theme-primary);
  font-weight: 700;
}
.tj-free-progress-bar .tj-progress-bar {
  height: 6px;
  background-color: #f2f3f5;
  border-radius: 10px;
  overflow: hidden;
}
.tj-free-progress-bar .tj-progress-bar .progress {
  background-color: var(--tj-color-theme-primary);
  height: 6px;
}
.tj-free-progress-bar .progress-bar-striped {
  background-image: linear-gradient(45deg, rgb(255, 255, 255) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0.5) 75%, transparent 75%, transparent);
}

.cartmini__widget .woocommerce-mini-cart__total {
  padding: 20px;
  margin-bottom: 0;
}
.cartmini__widget .woocommerce-mini-cart__total span {
  float: right;
}
.cartmini__widget .product_list_widget {
  max-height: 700px;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: thin;
  scrollbar-color: rgba(245, 9, 99, 0.5) #fff;
}
.cartmini__widget .product_list_widget::-webkit-scrollbar {
  display: thin; /* for Chrome, Safari, and Opera */
}
.cartmini__widget .woocommerce-mini-cart__buttons {
  padding: 20px;
}
.cartmini__widget .woocommerce-mini-cart__buttons .button {
  display: inline-block;
  font-weight: 500;
  color: var(--tj-color-heading-primary);
  font-family: var(--tj-ff-body);
  padding: 10px 30px;
  font-size: 16px;
  text-transform: capitalize;
  border: 1px solid #e7e8eb;
  text-align: center;
  width: 100%;
}
.cartmini__widget .woocommerce-mini-cart__buttons .button:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
}
.cartmini__widget .woocommerce-mini-cart__buttons .button:not(.checkout) {
  background-color: var(--tj-color-theme-bg);
  color: var(--tj-color-heading-primary);
  margin-bottom: 15px;
}
.cartmini__widget .woocommerce-mini-cart__buttons .button:not(.checkout):hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-heading-primary);
  border-color: var(--tj-color-heading-primary);
}

.woosq-open .select2-container {
  z-index: 9;
}

/* product sidebar */
div.product-widget {
  padding: 35px 30px;
  border: 1px solid var(--tj-color-border-2);
  /* rating filter */
  /* product filter by color list*/
  /* filter categories */
}
div.product-widget:not(:last-child) {
  margin-bottom: 30px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  div.product-widget {
    padding: 20px;
  }
}
div.product-widget .product-widget-title {
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 12px;
  font-size: 20px;
}
div.product-widget .product-widget-title::before, div.product-widget .product-widget-title::after {
  position: absolute;
  content: "";
  bottom: 0;
  height: 3px;
  background: var(--tj-color-theme-primary);
}
div.product-widget .product-widget-title::before {
  inset-inline-start: 0;
  width: 40px;
}
div.product-widget .product-widget-title::after {
  inset-inline-start: 45px;
  width: 10px;
}
div.product-widget .select2-container {
  margin-bottom: 20px;
}
div.product-widget .select2-container .select2-selection--single {
  height: 40px;
  line-height: 38px;
  border: 1px solid #e7e8eb;
  background-color: var(--tj-color-common-white);
  border-radius: 0;
}
div.product-widget .select2-container .select2-selection--single:focus {
  background-color: var(--tj-color-common-white);
}
div.product-widget .select2-container .select2-selection--single:focus .select2-selection--single {
  background-color: var(--tj-color-common-white);
  border-color: var(--tj-color-theme-primary);
}
div.product-widget .select2-container .select2-selection__rendered {
  line-height: 40px;
  -webkit-padding-start: 20px;
          padding-inline-start: 20px;
  -webkit-padding-end: 20px;
          padding-inline-end: 20px;
}
div.product-widget .select2-container .select2-selection__arrow {
  height: 40px;
  inset-inline-end: 20px;
}
div.product-widget .select2-container--default .select2-selection--single .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: 700;
  -webkit-padding-end: 4px;
          padding-inline-end: 4px;
  z-index: 2;
  background-color: var(--tj-color-common-white);
  top: -2px;
}
div.product-widget.widget_rating_filter ul li {
  list-style: none;
}
div.product-widget .woocommerce-widget-layered-nav-list li {
  list-style: none;
  font-size: 14px;
  color: #998f8f;
  margin-bottom: 5px;
}
div.product-widget .woocommerce-widget-layered-nav-list li span {
  font-weight: 400;
}
div.product-widget .woocommerce-widget-layered-nav-list li a {
  position: relative;
  -webkit-padding-start: 15px;
          padding-inline-start: 15px;
  color: var(--tj-color-text-body);
}
div.product-widget .woocommerce-widget-layered-nav-list li a:hover {
  color: var(--tj-color-theme-primary);
}
div.product-widget .woocommerce-widget-layered-nav-list li a::after {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  top: 53%;
  height: 4px;
  width: 4px;
  background-color: var(--tj-color-text-body);
  border-radius: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
div.product-widget .woocommerce-widget-layered-nav-list li a:hover {
  color: var(--tj-color-theme-primary);
}
div.product-widget.widget_product_categories ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
div.product-widget.widget_product_categories ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  position: relative;
  font-size: 16px;
  font-family: var(--tj-ff-heading);
  font-weight: var(--tj-fw-sbold);
  padding: 15px 24px;
  margin-bottom: 20px;
  color: var(--tj-color-heading-primary);
  background: var(--tj-color-theme-bg);
  z-index: 1;
  -webkit-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
}
div.product-widget.widget_product_categories ul li:last-child {
  margin-bottom: 0;
}
div.product-widget.widget_product_categories ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  color: var(--tj-color-heading-primary);
}
div.product-widget.widget_product_categories ul li:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
}
div.product-widget.widget_product_categories ul li:hover a {
  color: var(--tj-color-common-white);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  div.product-widget.widget_product_categories ul li {
    padding: 15px;
  }
}
div.product-widget.widget_top_rated_products ul li, div.product-widget.widget_recent_reviews ul li, div.product-widget.widget_products ul li {
  list-style: none;
}
div.product-widget.widget_top_rated_products ul li:not(:last-child), div.product-widget.widget_recent_reviews ul li:not(:last-child), div.product-widget.widget_products ul li:not(:last-child) {
  margin-bottom: 20px;
}
div.product-widget .tj-product-sidebar-rating-thumb img {
  max-width: inherit;
  width: 90px;
}
div.product-widget .tj-product-sidebar-rating-title {
  font-size: 16px;
}
div.product-widget .tj-product-sidebar-rating-title a:hover {
  color: var(--tj-color-theme-primary);
}
div.product-widget .tj-product-sidebar-rating-price {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 8px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}
div.product-widget .tj-product-sidebar-rating-price span {
  font-family: var(--tj-ff-body);
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-text-body);
}
div.product-widget .tj-product-sidebar-rating-price del,
div.product-widget .tj-product-sidebar-rating-price ins {
  text-decoration: none;
}
div.product-widget .tj-product-sidebar-rating-price del {
  color: var();
}
div.product-widget .tj-product-sidebar-rating-price del .woocommerce-Price-amount,
div.product-widget .tj-product-sidebar-rating-price del span {
  text-decoration-line: line-through;
  color: var(--tj-color-common-black-2);
}
div.product-widget .tj-product-sidebar-rating-content .star-rating {
  display: block;
}
div.product-widget .tj-product-sidebar-rating-content .reviewer span {
  font-weight: 400;
}
div.product-widget .tj-product-sidebar-rating-content .reviewer span span {
  font-weight: 500;
}
div.product-widget.widget_product_tag_cloud .tagcloud a {
  font-size: 16px !important;
  border: 1px solid var(--tj-color-theme-bg);
  -webkit-box-shadow: none;
          box-shadow: none;
  background-color: var(--tj-color-theme-bg);
  padding: 5px 10px;
}
div.product-widget.widget_product_tag_cloud .tagcloud a:hover {
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}

/* price range slider */
.tj-shop-sidebar .price_slider_wrapper {
  padding: 30px 20px;
  background-color: var(--tj-color-theme-bg);
}
.tj-shop-sidebar .ui-widget-content {
  position: relative;
  height: 4px;
  background-color: var(--tj-color-border-2);
  margin-bottom: 30px;
}
.tj-shop-sidebar .ui-widget-content .ui-slider-range {
  position: absolute;
  display: block;
  width: 100%;
  height: 4px;
  border: 0;
  background-color: var(--tj-color-theme-dark);
  border-radius: 8px;
  z-index: 1;
}
.tj-shop-sidebar .ui-widget-content .ui-slider-handle {
  inset-inline-start: 0%;
  position: absolute;
  z-index: 2;
  outline: 0;
  cursor: pointer;
  background-color: var(--tj-color-common-white);
  border-radius: 100%;
  border: 2px solid var(--tj-color-theme-dark);
  height: 14px;
  top: -5px;
  width: 14px;
  margin: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-transform: translateX(0px);
  -ms-transform: translateX(0px);
      transform: translateX(0px);
  -webkit-transition: unset;
  transition: unset;
}
.tj-shop-sidebar .ui-widget-content .ui-slider-handle:last-child {
  inset-inline-start: 100%;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
      transform: translateX(-100%);
}
.tj-shop-sidebar .price_slider_amount {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.tj-shop-sidebar .price_slider_amount .button {
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 2;
  order: 2;
  font-size: 16px;
  font-weight: 700;
  height: auto;
  -webkit-margin-start: auto;
          margin-inline-start: auto;
  padding: 8px 23px;
  border-radius: 30px;
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.tj-shop-sidebar .price_slider_amount .button:hover {
  background-color: var(--tj-color-theme-dark);
}
.tj-shop-sidebar .price_slider_amount .price_label {
  font-size: 16px;
  line-height: 1;
  padding: 12px 15px;
  background-color: var(--tj-color-common-white);
  border: 1px solid var(--tj-color-border-2);
}
.tj-shop-sidebar .price_slider_amount .price_label span {
  font-weight: 500;
  color: var(--tj-color-heading-primary);
}

.product-widget.woocommerce.widget_recent_reviews ul li {
  list-style: none;
}

/* product sidebar css end */
.woocommerce-ordering .nice-select ul.list {
  width: auto;
  inset-inline-start: auto;
  inset-inline-end: 0;
}

.tj-product-details-price del .woocommerce-Price-amount bdi, .tj-product-details-price del .woocommerce-Price-currencySymbol bdi {
  -webkit-margin-end: 7px;
          margin-inline-end: 7px;
}
.tj-product-details-price .price ins {
  text-decoration: none;
}

.woosc-quick-table-products .woosc_table td img {
  width: 100px;
}
.woosc-quick-table-products .woosc_table .star-rating {
  display: inline-block;
}
.woosc-quick-table-products .woosc_table a.added_to_cart.wc-forward {
  background: var(--tj-color-theme-primary);
  padding: 5px 20px;
  color: #fff;
}
.woosc-quick-table-products .woosc_table a.ajax_add_to_cart.added {
  display: none !important;
}

.woocommerce-product-gallery ol.product-thumbnails {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  margin-top: 20px;
}

.outofstock .woocommerce-grouped-product-list-item__quantity .cart-button {
  display: none;
}

.stock.out-of-stock {
  color: var(--tj-color-theme-primary);
  display: inline-block;
  margin-bottom: 5px;
}

.tj-product-area .nice-select.open .list {
  width: auto;
}

@media (max-width: 575px) {
  .woocommerce-ordering .nice-select ul.list {
    inset-inline-start: auto;
  }
}
.tj-product-details__list-img .tj-product__thumb-topsall {
  inset-inline-start: 25px;
  inset-inline-end: auto;
  position: absolute;
  top: 25px;
  padding: 10px 12px 10px;
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-text-body);
  font-size: 13px;
  line-height: 1;
  z-index: 2;
}

/*mini cart */
.tj-mini-card {
  display: inline-block;
  position: relative;
  cursor: pointer;
}
.tj-mini-card div.mini_shopping_cart_box {
  position: absolute;
  top: 110%;
  inset-inline-end: 0;
  width: 300px;
  background: #fff;
  padding: 1.25rem;
  background-color: #fff;
  -webkit-box-shadow: 0px 5px 10px rgba(62, 68, 90, 0.1);
  box-shadow: 0px 5px 10px rgba(62, 68, 90, 0.1);
  border: 1px solid #edeef5;
  border-radius: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0 !important;
  visibility: hidden;
  z-index: 10;
  text-align: start;
}
@media (max-width: 767px) {
  .tj-mini-card div.mini_shopping_cart_box {
    display: none;
  }
}
.tj-mini-card div.mini_shopping_cart_box .cartmini__empty .tj-btn {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  font-size: 14px;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  border-bottom: 1px solid #edeef5;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__thumb a {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__thumb a img {
  width: 60px;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .mini-cart-remove a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content {
  position: static;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content h5 {
  margin-bottom: 0;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content a {
  font-size: 14px;
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
  font-weight: 600;
  display: inline-block;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content .cartmini__price-wrapper {
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content .cartmini__price-wrapper .quantity {
  display: inline-block;
  -webkit-margin-end: 0;
          margin-inline-end: 0;
  margin-bottom: 0;
  font-size: 14px;
}
.tj-mini-card div.mini_shopping_cart_box .mini_cart_item .cartmini__content .cartmini__price-wrapper .quantity .woocommerce-Price-amount.amount {
  color: var(--tj-color-theme-primary);
}
.tj-mini-card div.mini_shopping_cart_box .woocommerce-mini-cart__total {
  margin-top: 20px;
}
.tj-mini-card div.mini_shopping_cart_box .woocommerce-mini-cart__total > strong {
  font-size: 16px;
  font-weight: 600;
  color: #c2c2d3;
}
.tj-mini-card div.mini_shopping_cart_box .woocommerce-mini-cart__total .woocommerce-Price-amount {
  float: right;
}
.tj-mini-card div.mini_shopping_cart_box .woocommerce-mini-cart__total .woocommerce-Price-amount bdi {
  font-weight: 600;
  color: var(--tj-color-theme-primary);
}
.tj-mini-card div.mini_shopping_cart_box .woocommerce-mini-cart__buttons .wc-forward {
  float: none;
  color: #222;
  display: block;
  border: 1px solid #ddd;
  text-align: center;
  padding: 7px 15px;
  margin-top: 10px;
  background: none;
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  font-size: 20px;
}
.tj-mini-card div.mini_shopping_cart_box .woocommerce-mini-cart__buttons .wc-forward.checkout {
  background: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
  color: #fff;
}

.tj-mini-card:hover .mini_shopping_cart_box {
  opacity: 1 !important;
  visibility: visible;
  top: 105%;
}

#woosq-popup .variations select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-radius: 5px;
  border: solid 1px #e8e8e8;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  float: left;
  font-family: inherit;
  font-size: 14px;
  font-weight: normal;
  height: 42px;
  line-height: 40px;
  outline: none;
  -webkit-padding-start: 18px;
          padding-inline-start: 18px;
  -webkit-padding-end: 30px;
          padding-inline-end: 30px;
  position: relative;
  text-align: left !important;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: auto;
}
#woosq-popup .star-rating {
  display: inline-block;
}

#review_form .comment-input label {
  font-size: 16px;
  color: var(--tj-color-text-body);
  font-weight: 500;
  margin-bottom: 10px;
}
#review_form .comment-respond > h3 {
  line-height: 1;
  margin-bottom: 20px;
  margin-top: 25px;
}
#review_form .comment-respond button {
  margin-top: 25px;
}

.woocommerce-cart-form .tj-product-details__quantity {
  padding: 10px 15px;
}
.woocommerce-cart-form input {
  width: 100px;
}

#customer_login {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
@media (max-width: 768px) {
  #customer_login {
    display: block;
  }
}
#customer_login .u-column1, #customer_login .u-column2 {
  width: 50%;
  display: inline-block;
  padding: 20px;
}
@media (max-width: 768px) {
  #customer_login .u-column1, #customer_login .u-column2 {
    width: 100%;
  }
}
#customer_login .u-column1 h2, #customer_login .u-column2 h2 {
  font-size: 28px;
  font-weight: 600;
  color: var(--tj-color-heading-primary);
  margin-bottom: 15px;
}
#customer_login .u-column1 label, #customer_login .u-column2 label {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  font-size: 16px;
  margin-bottom: 10px;
}
#customer_login .u-column1 label.woocommerce-form__label-for-checkbox, #customer_login .u-column2 label.woocommerce-form__label-for-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
#customer_login .u-column1 label.woocommerce-form__label-for-checkbox span, #customer_login .u-column2 label.woocommerce-form__label-for-checkbox span {
  font-size: 16px;
  cursor: pointer;
}
#customer_login .u-column1 input, #customer_login .u-column2 input {
  border-radius: 6px;
  padding: 0px 20px;
  font-size: 14px;
  width: 100%;
  height: 55px;
  border: 0;
  outline: 0;
  font-weight: 500;
  font-size: 16px;
  color: #87888a;
  -webkit-box-shadow: inset 0 0 0 1px #e5e5e5;
          box-shadow: inset 0 0 0 1px #e5e5e5;
  -webkit-transition: -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3), -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  color: var(--tj-color-heading-primary);
}
#customer_login .u-column1 input[type=checkbox], #customer_login .u-column2 input[type=checkbox] {
  width: 15px;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
  cursor: pointer;
}
#customer_login .u-column1 input:focus, #customer_login .u-column2 input:focus {
  -webkit-box-shadow: 0px 1px 2px 1px rgba(32, 33, 36, 0.06), inset 0 0 0 2px var(--tj-color-theme-primary);
          box-shadow: 0px 1px 2px 1px rgba(32, 33, 36, 0.06), inset 0 0 0 2px var(--tj-color-theme-primary);
}
#customer_login .u-column1 .woocommerce-form-login__submit, #customer_login .u-column1 .woocommerce-form-register__submit, #customer_login .u-column2 .woocommerce-form-login__submit, #customer_login .u-column2 .woocommerce-form-register__submit {
  font-family: var(--tj-ff-body);
  font-weight: 600;
  font-size: 16px;
  line-height: 1;
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  display: inline-block;
  padding: 12px 32px;
  border-radius: 4px;
  border: 2px solid var(--tj-color-theme-primary);
}
#customer_login .u-column1 .woocommerce-form-login__submit:hover, #customer_login .u-column1 .woocommerce-form-register__submit:hover, #customer_login .u-column2 .woocommerce-form-login__submit:hover, #customer_login .u-column2 .woocommerce-form-register__submit:hover {
  color: var(--tj-color-theme-primary);
  background-color: transparent;
}
#customer_login .u-column1 .lost_password, #customer_login .u-column2 .lost_password {
  font-size: 16px;
  color: #6f7172;
  text-decoration: underline;
}
#customer_login .u-column1 .lost_password:hover, #customer_login .u-column2 .lost_password:hover {
  color: var(--tj-color-theme-primary);
}

.woocommerce-ResetPassword label {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  font-size: 16px;
  margin-bottom: 10px;
}
.woocommerce-ResetPassword label.woocommerce-form__label-for-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.woocommerce-ResetPassword label.woocommerce-form__label-for-checkbox span {
  font-size: 16px;
  cursor: pointer;
}
.woocommerce-ResetPassword input {
  border-radius: 0;
  padding: 0px 20px;
  font-size: 14px;
  width: 100%;
  height: 55px;
  border: 0;
  outline: 0;
  font-weight: 500;
  font-size: 16px;
  color: #87888a;
  -webkit-box-shadow: inset 0 0 0 1px #e5e5e5;
          box-shadow: inset 0 0 0 1px #e5e5e5;
  -webkit-transition: -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3), -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  color: var(--tj-color-heading-primary);
}
.woocommerce-ResetPassword input[type=checkbox] {
  width: 15px;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
  cursor: pointer;
}
.woocommerce-ResetPassword input:focus {
  -webkit-box-shadow: 0px 1px 1px 1px rgba(32, 33, 36, 0.06), inset 0 0 0 1px #4260ff;
          box-shadow: 0px 1px 1px 1px rgba(32, 33, 36, 0.06), inset 0 0 0 1px #4260ff;
}
.woocommerce-ResetPassword .woocommerce-Button {
  height: 60px;
  padding: 0 50px;
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  text-transform: capitalize;
  color: var(--tj-color-common-white);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 6px;
  background: var(--tj-color-theme-primary);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
  border-radius: 60px;
}
.woocommerce-ResetPassword .woocommerce-Button:focus, .woocommerce-ResetPassword .woocommerce-Button:hover {
  color: var(--tj-color-common-white);
  background: var(--tj-color-theme-dark);
}

.woocommerce-notices-wrapper .woocommerce-error {
  padding: 0;
  list-style: none;
}
.woocommerce-notices-wrapper .woocommerce-error li {
  background: var(--tj-color-theme-bg);
  padding: 15px 20px;
  margin-bottom: 30px;
  border: 1px solid var(--tj-color-theme-bg);
  color: var(--tj-color-text-body);
}

.woocommerce:not(:has(#customer_login)) .woo-login-form {
  max-width: 645px;
  margin: 0 auto;
}
.woocommerce .woo-login-form h3 {
  margin-bottom: 20px;
}
.woocommerce .woo-lost-password {
  max-width: 645px;
  margin: 0 auto;
}

.woocommerce-form-register label,
.woocommerce-form-login label {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  margin-bottom: 5px;
  font-size: 16px;
}
.woocommerce-form-register label.woocommerce-form__label-for-checkbox,
.woocommerce-form-login label.woocommerce-form__label-for-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.woocommerce-form-register label.woocommerce-form__label-for-checkbox span,
.woocommerce-form-login label.woocommerce-form__label-for-checkbox span {
  font-size: 16px;
}
.woocommerce-form-register input,
.woocommerce-form-login input {
  background: transparent !important;
  border: 1px solid var(--tj-color-theme-dark) !important;
  border-radius: 0;
  height: 48px !important;
  padding: 0 0 0 10px;
  width: 100%;
  outline: none;
  -webkit-box-shadow: none;
  -ms-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
  display: block;
}
.woocommerce-form-register input[type=checkbox],
.woocommerce-form-login input[type=checkbox] {
  width: 15px;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
}
.woocommerce-form-register input:focus,
.woocommerce-form-login input:focus {
  background: var(--tj-color-theme-bg) !important;
}
.woocommerce-form-register .show-password-input,
.woocommerce-form-login .show-password-input {
  display: none;
}
.woocommerce-form-register .woocommerce-button,
.woocommerce-form-login .woocommerce-button {
  width: 100%;
  display: block;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 30px;
  border-radius: 0;
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.woocommerce-form-register .woocommerce-button .btn-text,
.woocommerce-form-login .woocommerce-button .btn-text {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  overflow: hidden;
  color: inherit;
  text-shadow: 0 23px 0 currentColor;
}
.woocommerce-form-register .woocommerce-button .btn-text span,
.woocommerce-form-login .woocommerce-button .btn-text span {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
  -webkit-transition: 0.5s;
  transition: 0.5s;
}
.woocommerce-form-register .woocommerce-button:hover,
.woocommerce-form-login .woocommerce-button:hover {
  background-color: var(--tj-color-theme-dark);
  color: var(--tj-color-common-white);
}
.woocommerce-form-register .woocommerce-button:hover .btn-text span,
.woocommerce-form-login .woocommerce-button:hover .btn-text span {
  -webkit-transform: translateY(-24px);
      -ms-transform: translateY(-24px);
          transform: translateY(-24px);
}

.woocommerce-address-fields .nice-select {
  display: block;
  width: 100%;
  border-radius: 0;
}

#shipping_address_1 {
  margin-bottom: 15px;
}

.woocommerce-address-fields label,
.edit-account label {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  font-size: 14px;
  margin-bottom: 10px;
}
.woocommerce-address-fields label.woocommerce-form__label-for-checkbox,
.edit-account label.woocommerce-form__label-for-checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.woocommerce-address-fields label.woocommerce-form__label-for-checkbox span,
.edit-account label.woocommerce-form__label-for-checkbox span {
  font-size: 16px;
  cursor: pointer;
}
.woocommerce-address-fields input,
.edit-account input {
  border-radius: 0;
  padding: 0px 20px;
  font-size: 14px;
  width: 100%;
  height: 55px;
  border: 0;
  outline: 0;
  font-weight: 500;
  font-size: 16px;
  color: #87888a;
  -webkit-box-shadow: inset 0 0 0 1px #e5e5e5;
          box-shadow: inset 0 0 0 1px #e5e5e5;
  -webkit-transition: -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  transition: box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3), -webkit-box-shadow 0.3s cubic-bezier(0.3, 0, 0, 0.3);
  color: var(--tj-color-heading-primary);
}
.woocommerce-address-fields input[type=checkbox],
.edit-account input[type=checkbox] {
  width: 15px;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
  cursor: pointer;
}
.woocommerce-address-fields input:focus,
.edit-account input:focus {
  -webkit-box-shadow: 0px 1px 1px 1px rgba(32, 33, 36, 0.06), inset 0 0 0 1px #4260ff;
          box-shadow: 0px 1px 1px 1px rgba(32, 33, 36, 0.06), inset 0 0 0 1px #4260ff;
}
.woocommerce-address-fields button[name=save_address], .woocommerce-address-fields .woocommerce-Button,
.edit-account button[name=save_address],
.edit-account .woocommerce-Button {
  height: 60px;
  padding: 0 50px;
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  text-transform: capitalize;
  color: var(--tj-color-common-white);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 6px;
  background: var(--tj-color-theme-primary);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
  border-radius: 60px;
}
.woocommerce-address-fields button[name=save_address]:focus, .woocommerce-address-fields button[name=save_address]:hover, .woocommerce-address-fields .woocommerce-Button:focus, .woocommerce-address-fields .woocommerce-Button:hover,
.edit-account button[name=save_address]:focus,
.edit-account button[name=save_address]:hover,
.edit-account .woocommerce-Button:focus,
.edit-account .woocommerce-Button:hover {
  color: var(--tj-color-common-white);
  background: var(--tj-color-theme-dark);
}

.woosw-copy-label {
  color: #6f7172;
}

#woosw_copy_btn {
  padding: 3px 20px;
  background: var(--tj-color-theme-primary);
  color: #fff;
  font-weight: 500;
  border: none;
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
}

/*rating*/
.star-rating {
  position: relative;
  font-size: 12px;
  overflow: hidden;
  line-height: 1;
  display: inline-block;
}

.star-rating::before {
  font-family: "Font Awesome 5 Pro";
  content: "\f005\f005\f005\f005\f005";
  text-transform: uppercase;
  letter-spacing: 3px;
  color: var(--tj-color-theme-primary);
  font-size: 12px;
  line-height: 1;
}

.star-rating span {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  overflow: hidden;
  font-size: 0;
  width: -webkit-max-content !important;
  width: -moz-max-content !important;
  width: max-content !important;
}

.star-rating span::before {
  font-family: "Font Awesome 5 Pro";
  content: "\f005\f005\f005\f005\f005";
  text-transform: uppercase;
  letter-spacing: 3px;
  color: var(--tj-color-theme-primary);
  font-weight: 900;
  display: inline-block;
  font-size: 12px;
  line-height: 1;
}

/*rating end*/
a.ajax_add_to_cart.added {
  display: none !important;
}

.woocommerce p.stars a {
  position: relative;
  height: 1em;
  width: 1em;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
  color: var(--tj-color-theme-primary);
}

.woocommerce p.stars a::before {
  display: block;
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 1em;
  height: 1em;
  line-height: 1;
  font-family: var(--tj-ff-fontawesome);
  content: "\f005";
  text-indent: 0;
}

.woocommerce p.stars a:hover ~ a::before {
  content: "\f005";
  font-weight: 400;
}

.woocommerce p.stars:hover a::before {
  content: "\f005";
  font-weight: 700;
}

.woocommerce p.stars.selected a.active::before {
  content: "\f005";
  font-weight: 700;
}

.woocommerce p.stars.selected a.active ~ a::before {
  content: "\f005";
  font-weight: 400;
}

.woocommerce p.stars.selected a:not(.active)::before {
  content: "\f005";
  font-weight: 700;
}

/*spinner */
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.woocommerce a.button.loading::before,
.woocommerce button.button.loading::after,
.woocommerce input.button.loading::after {
  font-family: var(--tj-ff-fontawesome);
  content: "\f110";
  vertical-align: top;
  font-weight: 400;
  top: 0.618em;
  font-size: 14px;
  inset-inline-end: 1em;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  color: var(--tj-color-heading-primary);
  display: inline-block;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.tj-product-action .added_to_cart.wc-forward {
  font-size: 0;
}

.loading svg {
  display: none;
}

/*compare*/
.woosc-area {
  background: no-repeat !important;
}

/* archive */
.tj-product:hover {
  position: relative;
  z-index: 2;
}

section.woosc-quick-table > h2 {
  font-size: 26px;
  color: var(--tj-color-text-body);
  font-weight: 600;
}

a.cart-button.icon-btn.button.stock-out {
  cursor: not-allowed;
}

.grouped_form a {
  color: var(--tj-color-text-body);
}
.grouped_form a:hover {
  color: var(--tj-color-theme-primary);
}
.grouped_form .variations .nice-select {
  float: none;
}
.grouped_form .tj-product-details__quantity {
  padding: 5px 13px;
  -webkit-margin-end: 5px;
          margin-inline-end: 5px;
  margin-top: 0;
  margin-bottom: 0;
}
.grouped_form .tj-product-details__quantity .tj-cart-minus, .grouped_form .tj-product-details__quantity .tj-cart-plus {
  font-size: 14px;
}
.grouped_form .tj-product-details__quantity input {
  font-size: 14px;
}
.grouped_form del {
  font-size: 16px;
  color: var(--tj-color-theme-bg);
  font-weight: 600;
}
.grouped_form del .woocommerce-Price-amount {
  font-size: 16px;
  color: var(--tj-color-theme-bg);
  font-weight: 500;
}
.grouped_form ins {
  text-decoration: none;
}
.grouped_form ins .woocommerce-Price-amount {
  color: var(--tj-color-text-body);
  font-weight: 600;
  font-size: 16px;
}
.grouped_form .woocommerce-Price-amount {
  color: var(--tj-color-text-body);
  font-weight: 600;
  font-size: 16px;
}

.woocommerce-shop .woocommerce-notices-wrapper {
  display: none;
}
.woocommerce-shop .tj-product__thumb-topsall {
  position: absolute;
  inset-inline-end: 25px;
  top: 15px;
}
.woocommerce-shop .tj-product__thumb-topsall span {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-text-body);
  padding: 10px 12px 10px;
}
.woocommerce-shop .tj-shop-list-title {
  font-size: 16px;
  color: var(--tj-color-text-body);
  margin-bottom: 0;
}

.tj-shop-listing {
  gap: 20px;
}
.tj-shop-listing .woocommerce-notices-wrapper {
  display: none;
}

.tj-shop-listing-popup .nice-select {
  background: transparent;
  padding: 0 15px;
  border-radius: 0;
  width: 220px;
}
@media (max-width: 575px) {
  .tj-shop-listing-popup .nice-select {
    width: auto;
  }
}
.tj-shop-listing-popup .nice-select::after {
  border: 0;
  content: "\e91a";
  font-family: "solvior-icons" !important;
  height: auto;
  width: auto;
  -webkit-transform: unset;
      -ms-transform: unset;
          transform: unset;
  -webkit-transform-origin: unset;
      -ms-transform-origin: unset;
          transform-origin: unset;
  top: 6px;
}
.tj-shop-listing-popup .nice-select .list {
  border-radius: 0;
}
.tj-shop-listing-popup .orderby .current {
  -webkit-margin-end: 25px;
          margin-inline-end: 25px;
}

a.added_to_cart.wc-forward::before {
  content: "\f00c";
  font-family: var(--tj-ff-fontawesome);
  position: relative;
  font-size: 16px;
  font-weight: 400;
}

.tj-product-price .woocommerce-Price-amount {
  font-weight: 400;
  font-size: 16px;
  color: var(--tj-color-heading-primary);
}
.tj-product-price ins {
  text-decoration: none;
}
.tj-product-price del .woocommerce-Price-amount {
  font-weight: 400;
  font-size: 16px;
  color: var(--tj-color-text-body);
}

.tj-product-details__cart .product-add-cart-btn {
  -webkit-margin-end: 5px;
          margin-inline-end: 5px;
}
.tj-product-details__cart .product-add-cart-btn svg {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  -webkit-margin-end: 3px;
          margin-inline-end: 3px;
}
.tj-product-details__cart .product-action-btn .woosw-btn {
  font-size: 0;
  width: 48px;
  height: 48px;
  line-height: 47px;
  text-align: center;
  color: var(--tj-color-heading-primary);
  border: 1px solid #dadce0;
  border-radius: 4px;
}
.tj-product-details__cart .product-action-btn .woosw-btn:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.tj-product-details__cart .product-action-btn .woosw-btn::before {
  font-size: 16px;
  content: "\f004";
  font-family: var(--tj-ff-fontawesome);
  margin: 0;
}
.tj-product-details__cart .product-action-btn .woosw-btn.woosw-btn-added::before {
  font-weight: 700;
}
.tj-product-details__cart .product-action-btn .woosc-btn {
  font-size: 0;
  width: 48px;
  height: 48px;
  line-height: 47px;
  text-align: center;
  color: var(--tj-color-heading-primary);
  border: 1px solid #dadce0;
  border-radius: 4px;
}
.tj-product-details__cart .product-action-btn .woosc-btn:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.tj-product-details__cart .product-action-btn .woosc-btn::before {
  font-size: 16px;
  content: "\f0ec";
  font-family: var(--tj-ff-fontawesome);
  margin: 0;
}
.tj-product-details__cart .product-action-btn .woosc-btn.woosc-btn-added::before {
  font-size: 16px;
  content: "\f00c";
  font-family: var(--tj-ff-fontawesome);
  margin: 0;
  font-weight: 700;
}

#review_form_wrapper .stars {
  margin-top: 5px;
  margin-bottom: 0;
}

.related-products .woocommerce-notices-wrapper {
  display: none;
}
.related-products .tj-product__thumb-topsall {
  position: absolute;
  inset-inline-end: 25px;
  top: 15px;
}
.related-products .tj-product__thumb-topsall span {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-text-body);
  padding: 10px 12px 10px;
}

.flex-control-thumbs.product-thumbnails .slick-list {
  margin: 0 -6px;
}
.flex-control-thumbs.product-thumbnails .slick-slide {
  margin: 0 6px;
}
.flex-control-thumbs.product-thumbnails .slick-slide {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 0;
  -webkit-transition: border-color 0.3s cubic-bezier(0.28, 0.12, 0.22, 1);
  transition: border-color 0.3s cubic-bezier(0.28, 0.12, 0.22, 1);
}
.flex-control-thumbs.product-thumbnails .slick-slide.slick-active img.flex-active {
  border: 1px solid var(--tj-color-theme-primary);
}
.flex-control-thumbs.product-thumbnails .slick-slide:hover {
  cursor: pointer;
}

.product-thumbnails {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  margin-top: 15px;
}

.cart-wrapper .cart_totals table tr, .cart-wrapper .cart_totals table tbody {
  border: none;
}
.cart-wrapper .wc_payment_methods {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  list-style: none;
}
.cart-wrapper .wc_payment_methods .woocommerce-notice {
  list-style: none;
  color: var(--tj-color-theme-primary);
}
.cart-wrapper #order_review_heading {
  margin-bottom: 20px;
}
.cart-wrapper .order-review-wrapper table tbody, .cart-wrapper .order-review-wrapper table td, .cart-wrapper .order-review-wrapper table tfoot, .cart-wrapper .order-review-wrapper table th, .cart-wrapper .order-review-wrapper table thead, .cart-wrapper .order-review-wrapper table tr {
  border: none;
}
.cart-wrapper .order-review-wrapper table {
  width: 100%;
  border: 0;
}
.cart-wrapper .order-review-wrapper table thead th {
  border: none;
  border-bottom: 1px solid var(--tj-color-border-2);
  -webkit-padding-start: 30px;
          padding-inline-start: 30px;
  padding-bottom: 0.75rem;
  text-align: start;
  font-family: var(--tj-ff-heading);
  font-size: 16px;
  font-weight: 600;
  color: var(--tj-color-heading-primary);
}
.cart-wrapper .order-review-wrapper table thead th:last-child {
  text-align: start;
  -webkit-padding-end: 30px;
          padding-inline-end: 30px;
}
.cart-wrapper .order-review-wrapper table tbody tr {
  border-bottom: 1px solid var(--tj-color-border-2);
}
.cart-wrapper .order-review-wrapper table tbody tr.cart_item td {
  font-size: 16px;
  -webkit-padding-start: 30px;
          padding-inline-start: 30px;
}
.cart-wrapper .order-review-wrapper table tbody tr.cart_item td:first-child {
  border-top: medium none;
  color: var(--tj-color-text-body);
  font-weight: normal;
  text-align: start;
  vertical-align: middle;
  width: 50%;
}
.cart-wrapper .order-review-wrapper table tbody tr.cart_item td:last-child {
  -webkit-padding-end: 30px;
          padding-inline-end: 30px;
  text-align: start;
  color: var(--tj-color-text-body);
}
.cart-wrapper .order-review-wrapper table tbody tr.cart_item td strong {
  color: var(--tj-color-text-body);
}
.cart-wrapper .order-review-wrapper table tbody tr td {
  border: 0;
  padding: 15px 0;
}
.cart-wrapper .order-review-wrapper table tfoot tr {
  border-bottom: 1px solid var(--tj-color-border-2);
}
.cart-wrapper .order-review-wrapper table tfoot tr.woocommerce-shipping-totals.shipping th {
  border: none;
  border-bottom: 1px solid var(--tj-color-border-2);
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  color: var(--tj-color-heading-primary);
  padding: 15px 0;
  -webkit-padding-start: 30px;
          padding-inline-start: 30px;
  text-align: start;
  font-size: 14px;
  font-weight: 600;
}
.cart-wrapper .order-review-wrapper table tfoot tr.woocommerce-shipping-totals.shipping td {
  text-align: start;
  padding: 15px 0;
  -webkit-padding-start: 30px;
          padding-inline-start: 30px;
}
.cart-wrapper .order-review-wrapper table tfoot tr.cart-subtotal th {
  border: none;
  border-bottom: 1px solid var(--tj-color-border-2);
  color: var(--tj-color-heading-primary);
  text-align: start;
  font-size: 14px;
  font-weight: 600;
}
.cart-wrapper .order-review-wrapper table tfoot tr.cart-subtotal td {
  border: none;
  border-bottom: 1px solid var(--tj-color-border-2);
  color: var(--tj-color-heading-primary);
  text-align: start;
  font-size: 14px;
}
.cart-wrapper .order-review-wrapper table tfoot tr.order-total th {
  text-align: start;
  font-weight: 600;
  color: var(--tj-color-heading-primary);
}
.cart-wrapper .order-review-wrapper table tfoot tr.order-total td {
  text-align: start;
}
.cart-wrapper .order-review-wrapper table tfoot tr.order-total td span bdi {
  color: var(--tj-color-heading-primary);
  font-weight: 600;
}
.cart-wrapper .order-review-wrapper table tfoot tr td, .cart-wrapper .order-review-wrapper table tfoot tr th {
  border: 0;
  padding: 15px 0;
  -webkit-padding-start: 30px;
          padding-inline-start: 30px;
}

.tj-checkout-billing-wrapper {
  background-color: var(--tj-color-common-white);
}
.tj-checkout-billing-wrapper label {
  position: static;
  border: 0;
  margin-bottom: 7px;
  display: block;
  width: auto;
  color: var(--tj-color-heading-primary);
}
.tj-checkout-billing-wrapper label abbr.required {
  color: red;
  text-decoration: none;
}
.tj-checkout-billing-wrapper label span {
  font-size: 16px;
}
.tj-checkout-billing-wrapper .woocommerce-input-wrapper span {
  display: block;
}
.tj-checkout-billing-wrapper input[type=text], .tj-checkout-billing-wrapper input[type=email], .tj-checkout-billing-wrapper input[type=tel], .tj-checkout-billing-wrapper input[type=url], .tj-checkout-billing-wrapper input[type=password] {
  outline: 0;
  margin-bottom: 30px;
}
.tj-checkout-billing-wrapper input[type=text]:focus, .tj-checkout-billing-wrapper input[type=email]:focus, .tj-checkout-billing-wrapper input[type=tel]:focus, .tj-checkout-billing-wrapper input[type=url]:focus, .tj-checkout-billing-wrapper input[type=password]:focus {
  border-color: var(--tj-color-theme-primary);
}
.tj-checkout-billing-wrapper .nice-select {
  height: 48px;
  line-height: 36px;
  border-radius: 0;
  border: 1px solid var(--tj-color-theme-dark);
  color: var(--tj-color-heading-primary);
  float: unset;
  padding: 5px 22px;
  margin-bottom: 30px;
}
.tj-checkout-billing-wrapper .nice-select::after {
  inset-inline-end: 26px;
}
.tj-checkout-billing-wrapper .nice-select .list {
  max-height: 300px;
  overflow: auto;
}
.tj-checkout-billing-wrapper .select2-container .select2-selection--single .select2-selection__rendered {
  -webkit-padding-start: 25px;
          padding-inline-start: 25px;
  -webkit-padding-end: 35px;
          padding-inline-end: 35px;
}
.tj-checkout-billing-wrapper .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 0;
  inset-inline-end: 17px;
  height: 46px;
}
.tj-checkout-billing-wrapper button[name=save_address], .tj-checkout-billing-wrapper button[name=save_account_details] {
  border-radius: 0;
  border: 0;
  outline: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--tj-color-common-white);
  padding: 11px 45px;
  background-color: var(--tj-color-heading-primary);
  margin-bottom: 15px;
  text-transform: capitalize;
}
.tj-checkout-billing-wrapper button[name=save_address]:hover, .tj-checkout-billing-wrapper button[name=save_account_details]:hover {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.tj-checkout-billing-wrapper button[name=save_account_details] {
  margin-top: 20px;
}
.tj-checkout-billing-wrapper .woocommerce-form-row {
  margin-bottom: 0;
}
.tj-checkout-billing-wrapper button.woocommerce-form-login__submit[type=submit], .tj-checkout-billing-wrapper button.woocommerce-form-register__submit[name=register] {
  border-radius: 0;
  border: 0;
  outline: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--tj-color-common-white);
  padding: 11px 45px;
  background-color: var(--tj-color-theme-primary);
  margin-bottom: 15px;
  display: block;
}
.tj-checkout-billing-wrapper button.woocommerce-form-login__submit[type=submit]:hover, .tj-checkout-billing-wrapper button.woocommerce-form-register__submit[name=register]:hover {
  background-color: var(--tj-color-heading-primary);
  color: var(--tj-color-common-white);
}
.tj-checkout-billing-wrapper .woocommerce-privacy-policy-text p a {
  color: var(--tj-color-theme-primary);
}
.tj-checkout-billing-wrapper .woocommerce-privacy-policy-text p a:hover {
  text-decoration: underline !important;
}
.tj-checkout-billing-existing-login, .tj-checkout-billing-coupon {
  margin-bottom: 80px;
}
.tj-checkout-billing-existing-login label, .tj-checkout-billing-coupon label {
  position: static;
  border: 0;
  margin-bottom: 7px;
  display: inline-block;
  width: auto;
  color: var(--tj-color-heading-primary);
}
.tj-checkout-billing-existing-login label abbr.required, .tj-checkout-billing-coupon label abbr.required {
  color: red;
  text-decoration: none;
}
.tj-checkout-billing-existing-login .woocommerce-info, .tj-checkout-billing-coupon .woocommerce-info {
  font-size: 16px;
  color: var(--tj-color-heading-primary);
  display: inline-block;
  padding: 8px 26px;
  width: 100%;
}
.tj-checkout-billing-existing-login .woocommerce-info a, .tj-checkout-billing-coupon .woocommerce-info a {
  color: var(--tj-color-theme-primary);
  position: relative;
  border-bottom: 1px solid var(--tj-color-theme-primary);
}
.tj-checkout-billing-existing-login .checkout_coupon.woocommerce-form-coupon, .tj-checkout-billing-existing-login .woocommerce-form.woocommerce-form-login, .tj-checkout-billing-coupon .checkout_coupon.woocommerce-form-coupon, .tj-checkout-billing-coupon .woocommerce-form.woocommerce-form-login {
  margin-top: 14px;
  background-color: var(--tj-color-common-white);
  position: relative;
}
.tj-checkout-billing-existing-login input[type=text], .tj-checkout-billing-existing-login input[type=email], .tj-checkout-billing-existing-login input[type=tel], .tj-checkout-billing-existing-login input[type=url], .tj-checkout-billing-existing-login input[type=password], .tj-checkout-billing-coupon input[type=text], .tj-checkout-billing-coupon input[type=email], .tj-checkout-billing-coupon input[type=tel], .tj-checkout-billing-coupon input[type=url], .tj-checkout-billing-coupon input[type=password] {
  outline: 0;
  border-radius: 0;
  height: 46px;
  background: #ffffff;
  border: 1px solid #ced7e0;
  font-size: 14px;
  color: var(--tj-color-heading-primary);
  padding: 0 25px;
  line-height: 46px;
  margin-bottom: 0;
  -webkit-margin-end: 0;
          margin-inline-end: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-checkout-billing-existing-login input[type=text], .tj-checkout-billing-existing-login input[type=email], .tj-checkout-billing-existing-login input[type=tel], .tj-checkout-billing-existing-login input[type=url], .tj-checkout-billing-existing-login input[type=password], .tj-checkout-billing-coupon input[type=text], .tj-checkout-billing-coupon input[type=email], .tj-checkout-billing-coupon input[type=tel], .tj-checkout-billing-coupon input[type=url], .tj-checkout-billing-coupon input[type=password] {
    width: 100%;
  }
}
.tj-checkout-billing-existing-login input[type=text]:focus, .tj-checkout-billing-existing-login input[type=email]:focus, .tj-checkout-billing-existing-login input[type=tel]:focus, .tj-checkout-billing-existing-login input[type=url]:focus, .tj-checkout-billing-existing-login input[type=password]:focus, .tj-checkout-billing-coupon input[type=text]:focus, .tj-checkout-billing-coupon input[type=email]:focus, .tj-checkout-billing-coupon input[type=tel]:focus, .tj-checkout-billing-coupon input[type=url]:focus, .tj-checkout-billing-coupon input[type=password]:focus {
  border-color: var(--tj-color-theme-primary);
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme {
  margin-bottom: 15px;
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input {
  display: none;
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input:checked ~ span::after, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input:checked ~ span::after {
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input:checked ~ span::before, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input:checked ~ span::before {
  visibility: visible;
  opacity: 1;
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span {
  font-size: 16px;
  color: #55585b;
  position: relative;
  -webkit-padding-start: 26px;
          padding-inline-start: 26px;
  z-index: 1;
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span::after, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span::after {
  position: absolute;
  content: "";
  top: 2px;
  inset-inline-start: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  border: 1px solid #c3c7c9;
  z-index: -1;
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span::before, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span::before {
  position: absolute;
  content: url("../icons/check.svg");
  top: 2px;
  inset-inline-start: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  visibility: hidden;
  opacity: 0;
  color: var(--tj-color-common-white);
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span a:hover, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-checkout-billing-existing-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span:hover, .tj-checkout-billing-coupon .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span:hover {
  cursor: pointer;
}
.tj-checkout-billing-existing-login .lost_password a, .tj-checkout-billing-coupon .lost_password a {
  color: var(--tj-color-heading-primary);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.tj-checkout-billing-existing-login .lost_password a:hover, .tj-checkout-billing-coupon .lost_password a:hover {
  color: var(--tj-color-theme-primary);
}

.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme {
  margin-bottom: 15px;
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input {
  display: none;
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input:checked ~ span::after {
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme input:checked ~ span::before {
  visibility: visible;
  opacity: 1;
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span {
  font-size: 16px;
  color: #676e7a;
  position: relative;
  -webkit-padding-start: 26px;
          padding-inline-start: 26px;
  z-index: 1;
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span::after {
  position: absolute;
  content: "";
  top: 5px;
  inset-inline-start: 0;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border: 1px solid var(--tj-color-border-2);
  z-index: -1;
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span::before {
  position: absolute;
  content: "\f00c";
  inset-inline-start: 4px;
  top: 5px;
  text-align: center;
  visibility: hidden;
  opacity: 0;
  color: var(--tj-color-common-white);
  font-family: "Font Awesome 6 Pro";
  font-size: 10px;
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span a:hover {
  color: var(--tj-color-theme-primary);
}
.woocommerce-form.woocommerce-form-login .woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme span:hover {
  cursor: pointer;
}
.woocommerce-form.woocommerce-form-login .lost_password a {
  color: var(--tj-color-heading-primary);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.woocommerce-form.woocommerce-form-login .lost_password a:hover {
  color: var(--tj-color-theme-primary);
}

.woocommerce-MyAccount-content .woosw-list .woosw-items .woosw-item--add .add_to_cart_button {
  font-size: 16px;
}
.woocommerce-MyAccount-content .woosw-list .woosw-items .woosw-item--add .add_to_cart_button svg {
  -webkit-margin-end: 0;
          margin-inline-end: 0;
}

.woocommerce-MyAccount-content table.woosc_table thead th,
.woocommerce-MyAccount-content table.woosc_table tbody td {
  padding: 10px;
}
.woocommerce-MyAccount-content table.woosc_table thead th.th-placeholder, .woocommerce-MyAccount-content table.woosc_table thead th.td-placeholder,
.woocommerce-MyAccount-content table.woosc_table tbody td.th-placeholder,
.woocommerce-MyAccount-content table.woosc_table tbody td.td-placeholder {
  display: none;
}

.woocommerce-MyAccount-content table.woosc_table thead th a {
  font-weight: 500;
}
.woocommerce-MyAccount-content table.woosc_table thead th span {
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.woocommerce-MyAccount-content table.woosc_table thead th span:hover {
  color: var(--tj-color-theme-primary);
}

.cart-wrapper .woocommerce-checkout-review-order table {
  margin-bottom: 0;
  border: 1px solid var(--tj-color-border-2);
}

.woocommerce-checkout-payment {
  margin-top: 0;
}
.woocommerce-checkout-payment .wc_payment_methods {
  border: 1px solid var(--tj-color-border-2);
  border-top: 0;
  padding: 30px;
  padding-bottom: 30px;
  padding-top: 15px;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method {
  list-style: none;
  padding-top: 0;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method:not(:last-child) {
  margin-bottom: 10px;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method input {
  display: none;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method input:checked ~ label::before {
  opacity: 1;
  visibility: visible;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label {
  font-family: var(--tj-ff-heading);
  font-size: 16px;
  position: relative;
  -webkit-padding-start: 25px;
          padding-inline-start: 25px;
  font-weight: 500;
  color: var(--tj-color-text-body);
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label:hover {
  cursor: pointer;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label::after {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  top: 3px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--tj-color-theme-dark);
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label::before {
  position: absolute;
  content: "";
  inset-inline-start: 5px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--tj-color-theme-dark);
  visibility: hidden;
  opacity: 0;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label img {
  -webkit-margin-start: 14px;
          margin-inline-start: 14px;
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label a {
  -webkit-margin-start: 20px;
          margin-inline-start: 20px;
  position: relative;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method label a::after {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  bottom: 3px;
  width: 100%;
  height: 1px;
  background-color: var(--tj-color-heading-primary);
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method .payment_box {
  position: relative;
  padding-top: 10px;
  display: none;
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method .payment_box::after {
  position: absolute;
  content: "";
  inset-inline-start: 57px;
  top: 0;
  width: 16px;
  height: 16px;
  background-color: #f6f7f9;
  -webkit-transform: translateY(3px) rotate(45deg);
  -ms-transform: translateY(3px) rotate(45deg);
  transform: translateY(3px) rotate(45deg);
}
.woocommerce-checkout-payment .wc_payment_methods .wc_payment_method .payment_box p {
  background-color: #f6f7f9;
  padding: 10px 15px;
  -webkit-margin-start: 25px;
          margin-inline-start: 25px;
  font-size: 14px;
  line-height: 1.57;
  color: #55585b;
  margin-bottom: 0;
  padding-bottom: 0;
}
.woocommerce-checkout-payment .woocommerce-privacy-policy-text {
  margin-bottom: 25px;
}

/*checkcout */
#customer_details .woocommerce-billing-fields__field-wrapper,
#customer_details .woocommerce-additional-fields__field-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -webkit-margin-start: -10px;
          margin-inline-start: -10px;
  -webkit-margin-end: -10px;
          margin-inline-end: -10px;
}

#customer_details .woocommerce-billing-fields__field-wrapper .form-row,
#customer_details .woocommerce-additional-fields__field-wrapper .form-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
  -webkit-padding-start: 10px;
          padding-inline-start: 10px;
  -webkit-padding-end: 10px;
          padding-inline-end: 10px;
  -webkit-margin-start: 0;
          margin-inline-start: 0;
  -webkit-margin-end: 0;
          margin-inline-end: 0;
  margin-bottom: 0;
}

#billing_address_1_field {
  margin-bottom: 15px;
}

.woocommerce-checkout-review-order-table th {
  padding: 12px 12px;
}

.woocommerce-additional-fields h3 {
  margin-bottom: 20px;
}

.page-main-content table,
.page-main-content th,
.page-main-content td {
  border: 1px solid #ddd;
}

.woocommerce-MyAccount-navigation {
  margin-bottom: 30px;
}

.woosw-popup .woosw-popup-inner .woosw-popup-content {
  max-width: 500px;
}
.woosw-popup .woosw-popup-inner .woosw-popup-content a.wc-forward,
.woosw-popup .woosw-popup-inner .woosw-popup-content .tj-cart-btn {
  width: 100%;
}
.woosw-popup .woosw-popup-inner .woosw-popup-content a.tj-cart-btn .btn-text {
  display: none;
}
.woosw-popup .woosw-popup-inner .woosw-popup-content a.wc-forward {
  font-size: 0;
}

.rg-15 {
  row-gap: 15px;
}

.woosq-popup .woosq-product h3 {
  font-size: 28px;
}
.woosq-popup .woosq-product .thumbnails img {
  -o-object-fit: cover;
     object-fit: cover;
  margin: 0;
  width: 100%;
}
.woosq-popup .woosq-product .price {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  gap: 5px;
}
.woosq-popup .woosq-product .price span {
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-body-text);
  text-decoration: none;
}
.woosq-popup .woosq-product .price del,
.woosq-popup .woosq-product .price ins {
  text-decoration: none;
}
.woosq-popup .woosq-product .price del span {
  text-decoration-line: line-through;
  color: var(--tj-color-common-black-2);
}

div.woocommerce .woocommerce-cart-form table.shop_table td:before {
  content: attr(data-title);
  position: absolute;
  inset-inline-start: 15px;
  top: 50%;
  vertical-align: top;
  padding: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
      transform: translateY(-50%);
  display: none;
  color: var(--tj-color-heading-primary);
  font-family: var(--tj-ff-heading);
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.2;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  div.woocommerce .woocommerce-cart-form table.shop_table {
    border: 0;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table thead {
    display: none;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table tbody tr {
    border: 0;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table tbody tr:last-child {
    border-bottom: 1px solid var(--tj-color-border-2);
  }
  div.woocommerce .woocommerce-cart-form table.shop_table td {
    position: relative;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table td {
    padding: 15px;
    display: block;
    width: 100%;
    -webkit-padding-start: 25%;
            padding-inline-start: 25%;
    text-align: end;
    border: 1px solid var(--tj-color-border-2);
    border-bottom: 0;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table td.product-subtotal, div.woocommerce .woocommerce-cart-form table.shop_table td:first-child {
    -webkit-border-end: 1px solid var(--tj-color-border-2);
            border-inline-end: 1px solid var(--tj-color-border-2);
  }
  div.woocommerce .woocommerce-cart-form table.shop_table td.product-remove, div.woocommerce .woocommerce-cart-form table.shop_table td.product-name h5 {
    text-align: end;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table td.product-quantity .tj-product-quantity {
    width: 136px;
    text-align: end;
    -webkit-margin-start: auto;
            margin-inline-start: auto;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table td::before {
    display: block;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table tbody tr td.product-thumbnail {
    width: auto;
  }
  div.woocommerce .woocommerce-cart-form table.shop_table tbody tr td.product-thumbnail img {
    width: 100px;
  }
}

.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option--highlighted[data-selected] {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}

/*----------------------------------------*/
/*  3. PRODUCT CSS START
/*----------------------------------------*/
.row {
  --bs-gutter-x: 30px;
}

.tj-product-item:hover .tj-product-action {
  visibility: visible;
  opacity: 1;
  inset-inline-end: 15px;
}
.tj-product-item:hover .tj-product-cart-btn {
  -webkit-transform: translateY(-15px);
      -ms-transform: translateY(-15px);
          transform: translateY(-15px);
  opacity: 1;
  visibility: visible;
}
.tj-product-thumb {
  position: relative;
  border-radius: 0;
  background-color: var(--tj-color-theme-bg);
  overflow: hidden;
  margin-bottom: 25px;
}
.tj-product-thumb img {
  width: 100%;
}
.tj-product-badge {
  position: absolute;
  top: 15px;
  inset-inline-start: 15px;
  z-index: 1;
}
.tj-product-badge span {
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  border-radius: 40px;
  display: inline-block;
  padding: 6px 10px;
}
.tj-product-badge span.sold-out {
  background-color: #ff0004;
}
.tj-product-title {
  font-size: 20px;
  margin-bottom: 5px;
}
.tj-product-title a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-tag {
  margin-bottom: 2px;
}
.tj-product-tag a {
  font-size: 14px;
  position: relative;
  display: inline-block;
  line-height: 1;
}
.tj-product-tag a::after {
  position: absolute;
  content: "";
  inset-inline-start: auto;
  inset-inline-end: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  background-color: var(--tj-color-theme-primary);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.tj-product-tag a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-tag a:hover::after {
  inset-inline-start: 0;
  inset-inline-end: auto;
  width: 100%;
}
.tj-product-price-wrapper .price {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  gap: 5px;
}
.tj-product-price-wrapper .price span {
  font-weight: 500;
  font-size: 16px;
  color: var(--tj-color-body-text);
  text-decoration: none;
}
.tj-product-price-wrapper .price del,
.tj-product-price-wrapper .price ins {
  text-decoration: none;
}
.tj-product-price-wrapper .price del span {
  text-decoration-line: line-through;
  color: var(--tj-color-common-black-2);
}
.tj-product-action {
  position: absolute;
  inset-inline-end: -50px;
  top: 15px;
  z-index: 1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.tj-product-action-item {
  gap: 10px;
}
.tj-product-action-btn {
  position: relative;
}
.tj-product-action-btn a, .tj-product-action-btn button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  font-size: 0;
  color: var(--tj-color-theme-dark);
  background-color: var(--tj-color-common-white);
  border-bottom: 0;
  -webkit-box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
          box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.tj-product-action-btn a i, .tj-product-action-btn button i {
  font-size: 16px;
}
.tj-product-action-btn a:hover, .tj-product-action-btn a.loading:hover, .tj-product-action-btn button:hover, .tj-product-action-btn button.loading:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-dark);
}
.tj-product-action-btn a:hover::before, .tj-product-action-btn a:hover::after, .tj-product-action-btn a.loading:hover::before, .tj-product-action-btn a.loading:hover::after, .tj-product-action-btn button:hover::before, .tj-product-action-btn button:hover::after, .tj-product-action-btn button.loading:hover::before, .tj-product-action-btn button.loading:hover::after {
  color: var(--tj-color-common-white) !important;
}
.tj-product-action-btn button::before {
  font-size: 16px;
  content: "\f004";
  font-family: var(--tj-ff-fontawesome);
  margin: 0;
  font-weight: 300;
  display: inline-block;
}
.tj-product-action-btn button::before.woosw-btn.woosw-added::before {
  margin: 0;
}
.tj-product-action-btn-tooltip {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  inset-inline-end: 60px;
  font-weight: 500;
  font-size: 14px;
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-dark);
  z-index: 1;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  line-height: 1;
  padding: 8px 10px;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.tj-product-action-btn-tooltip::after {
  position: absolute;
  content: "";
  inset-inline-end: -10px;
  top: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
      -ms-transform: translate(-50%, -50%) rotate(45deg);
          transform: translate(-50%, -50%) rotate(45deg);
  height: 10px;
  width: 10px;
  background-color: var(--tj-color-theme-dark);
}
.tj-product-action-btn:hover .tj-product-action-btn-tooltip {
  visibility: visible;
  opacity: 1;
}
.tj-product-rating-icon {
  margin-bottom: 5px;
}
.tj-product-cart-btn {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  inset-inline-end: 0;
  -webkit-transform: translateY(60px);
      -ms-transform: translateY(60px);
          transform: translateY(60px);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.tj-product-area {
  overflow: hidden;
}

.tj-product-rating {
  margin-top: 3px;
}

.woosw-list table a.wc-forward,
.woosw-popup a.wc-forward,
.tj-product-item .wc-forward,
.tj-cart-btn {
  width: calc(100% - 30px);
  margin: 0 auto;
  height: 48px;
  padding: 0 30px;
  color: var(--tj-color-common-white);
  background: var(--tj-color-theme-dark);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 8px;
}
.woosw-list table a.wc-forward span.btn-icon,
.woosw-popup a.wc-forward span.btn-icon,
.tj-product-item .wc-forward span.btn-icon,
.tj-cart-btn span.btn-icon {
  overflow: hidden;
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--tj-color-common-white);
}
.woosw-list table a.wc-forward span.btn-icon i:first-child, .woosw-list table a.wc-forward span.btn-icon i:last-child,
.woosw-popup a.wc-forward span.btn-icon i:first-child,
.woosw-popup a.wc-forward span.btn-icon i:last-child,
.tj-product-item .wc-forward span.btn-icon i:first-child,
.tj-product-item .wc-forward span.btn-icon i:last-child,
.tj-cart-btn span.btn-icon i:first-child,
.tj-cart-btn span.btn-icon i:last-child {
  -webkit-transition: -webkit-transform 0.4s ease-in-out 0s;
  transition: -webkit-transform 0.4s ease-in-out 0s;
  transition: transform 0.4s ease-in-out 0s;
  transition: transform 0.4s ease-in-out 0s, -webkit-transform 0.4s ease-in-out 0s;
}
.woosw-list table a.wc-forward span.btn-icon i:last-child,
.woosw-popup a.wc-forward span.btn-icon i:last-child,
.tj-product-item .wc-forward span.btn-icon i:last-child,
.tj-cart-btn span.btn-icon i:last-child {
  position: absolute;
  -webkit-transform: translateX(-150%);
      -ms-transform: translateX(-150%);
          transform: translateX(-150%);
}
.woosw-list table a.wc-forward span.btn-text,
.woosw-popup a.wc-forward span.btn-text,
.tj-product-item .wc-forward span.btn-text,
.tj-cart-btn span.btn-text {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  overflow: hidden;
  color: var(--tj-color-common-white);
  text-shadow: 0 23px 0 currentColor;
  font-weight: 700;
}
.woosw-list table a.wc-forward span.btn-text > span,
.woosw-popup a.wc-forward span.btn-text > span,
.tj-product-item .wc-forward span.btn-text > span,
.tj-cart-btn span.btn-text > span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
  -webkit-transition: 0.5s;
  transition: 0.5s;
}
.woosw-list table a.wc-forward:hover,
.woosw-popup a.wc-forward:hover,
.tj-product-item .wc-forward:hover,
.tj-cart-btn:hover {
  background: var(--tj-color-theme-primary);
}
.woosw-list table a.wc-forward:hover span.btn-icon,
.woosw-popup a.wc-forward:hover span.btn-icon,
.tj-product-item .wc-forward:hover span.btn-icon,
.tj-cart-btn:hover span.btn-icon {
  color: var(--tj-color-common-white);
}
.woosw-list table a.wc-forward:hover span.btn-icon i:first-child,
.woosw-popup a.wc-forward:hover span.btn-icon i:first-child,
.tj-product-item .wc-forward:hover span.btn-icon i:first-child,
.tj-cart-btn:hover span.btn-icon i:first-child {
  -webkit-transform: translateX(150%);
      -ms-transform: translateX(150%);
          transform: translateX(150%);
}
.woosw-list table a.wc-forward:hover span.btn-icon i:last-child,
.woosw-popup a.wc-forward:hover span.btn-icon i:last-child,
.tj-product-item .wc-forward:hover span.btn-icon i:last-child,
.tj-cart-btn:hover span.btn-icon i:last-child {
  -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
          transform: translateX(0);
}
.woosw-list table a.wc-forward:hover span.btn-text,
.woosw-popup a.wc-forward:hover span.btn-text,
.tj-product-item .wc-forward:hover span.btn-text,
.tj-cart-btn:hover span.btn-text {
  color: var(--tj-color-common-white);
}
.woosw-list table a.wc-forward:hover span.btn-text > span,
.woosw-popup a.wc-forward:hover span.btn-text > span,
.tj-product-item .wc-forward:hover span.btn-text > span,
.tj-cart-btn:hover span.btn-text > span {
  -webkit-transform: translateY(-24px);
      -ms-transform: translateY(-24px);
          transform: translateY(-24px);
}

.tj-shop-listing-popup .nice-select {
  border-color: var(--tj-color-theme-bg);
}

.tj-shop-sidebar .ui-widget-content {
  border: 0;
  border-radius: 0;
}

div.woocommerce .woocommerce-cart-form table tbody tr td.product-thumbnail img {
  width: 92px;
  -o-object-fit: cover;
     object-fit: cover;
}

div.woocommerce .woocommerce-cart-form table tbody tr td.product-remove a {
  font-weight: var(--tj-fw-bold);
}

div.woocommerce .cart_totals table {
  margin-bottom: 30px;
}

.woosq-product.container {
  --bs-gutter-x: 0;
}
@media (max-width: 575px) {
  .woosq-product.container {
    margin: 0 15px;
    width: calc(100% - 30px);
  }
}
.woosq-product.container .row {
  --bs-gutter-x: 0;
}

.vbox-overlay:has(.woosq-product.container) .swiper-button-prev, .vbox-overlay:has(.woosq-product.container) .swiper-button-next {
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  padding: 0 0 0 0;
  margin: 0 0 0 0;
  overflow: hidden;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  font-size: 0;
  color: #222;
  background-color: transparent;
  border-radius: 0;
  border: none !important;
  z-index: 8;
}
.vbox-overlay:has(.woosq-product.container) .swiper-button-prev::after, .vbox-overlay:has(.woosq-product.container) .swiper-button-next::after {
  font-size: 15px;
  line-height: 44px;
  font-weight: 800;
}
.vbox-overlay:has(.woosq-product.container) .swiper-button-prev:hover, .vbox-overlay:has(.woosq-product.container) .swiper-button-next:hover {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
}
.vbox-overlay:has(.woosq-product.container) .swiper-button-prev {
  left: 0;
}
.vbox-overlay:has(.woosq-product.container) .swiper-button-next {
  right: 0;
}
.vbox-overlay:has(.woosq-product.container) .swiper-pagination-bullet {
  background-color: var(--tj-color-common-black);
  opacity: 0.5;
}
.vbox-overlay:has(.woosq-product.container) .swiper-pagination-bullet-active {
  opacity: 1;
}
.vbox-overlay:has(.woosq-product.container) .vbox-backdrop {
  background-color: rgba(23, 23, 23, 0.8) !important;
}

.vbox-content:has(.woosq-product.container) {
  max-width: 920px;
  margin: 0 auto;
  padding: 0;
}
.vbox-content:has(.woosq-product.container) .vbox-child.vbox-inline {
  background-color: transparent !important;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.woosq-product.container .thumbnails .images {
  max-width: 460px;
}

.woosq-product.container {
  background-color: #ffffff;
  -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
          box-shadow: 0 0 12px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.woosq-product.container .thumbnail img {
  height: 460px;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.woosq-product.container .summary .summary-content {
  padding: 20px;
}

.tj-product-details-action-item-wrapper {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.woosq-product.container .price {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  gap: 5px;
}

/*----------------------------------------*/
/*  2. PRODUCT DETAILS CSS START
/*----------------------------------------*/
.tj-product-details-wrapper {
  -webkit-margin-start: 50px;
          margin-inline-start: 50px;
}
.tj-product-details-wrapper.has-sticky {
  position: sticky;
  top: 120px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tj-product-details-wrapper {
    -webkit-margin-start: 0;
            margin-inline-start: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-wrapper {
    -webkit-margin-start: 0;
            margin-inline-start: 0;
    margin-top: 50px;
  }
}
.tj-product-details-wrapper p {
  margin-bottom: 25px;
}
.tj-product-details-category {
  margin-bottom: 10px;
}
.tj-product-details-category span {
  font-size: 14px;
  line-height: 1;
  font-weight: 600;
}
.tj-product-details-category span a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-stock {
  margin-bottom: 22px;
}
.tj-product-details-stock span {
  display: inline-block;
  font-size: 14px;
  color: var(--tj-color-theme-primary);
  background-color: var(--tj-color-theme-bg);
  line-height: 1;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 30px;
}
.tj-product-details-rating {
  gap: 10px;
  margin-bottom: 22px;
}
.tj-product-details-rating .star-rating {
  display: inline-block;
  padding: 3px 4px 3px 8px;
  background-color: var(--tj-color-theme-bg);
  border-radius: 32px;
}
.tj-product-details-rating .star-rating::before {
  position: absolute;
}
.tj-product-details-rating .star-rating span {
  position: unset;
}
.tj-product-details-price-wrapper .price {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  margin-bottom: 20px;
}
.tj-product-details-price-wrapper span {
  font-weight: 600;
  font-size: 22px;
  color: var(--tj-color-text-body);
}
.tj-product-details-price-wrapper del {
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
  text-decoration: none;
}
.tj-product-details-price-wrapper del span {
  text-decoration-line: line-through;
  color: var(--tj-color-common-white-2);
}
.tj-product-details-price-wrapper ins {
  text-decoration: none;
}
.tj-product-details-action-item-wrapper {
  gap: 15px;
  margin-bottom: 30px;
  max-width: 405px;
}
.tj-product-details-variation {
  margin-bottom: 30px;
}
.tj-product-details-variation-title {
  font-size: 15px;
  font-weight: 400;
  margin-bottom: 4px;
}
.tj-product-details-variation-item:not(:last-child) {
  margin-bottom: 15px;
}
.tj-product-details-variation-list button {
  display: inline-block;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  position: relative;
}
.tj-product-details-variation-list button span[data-bg-color] {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  width: 100%;
  height: 100%;
  background-color: var(--tj-color-common-white);
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.tj-product-details-variation-list button .tj-color-variation-tootltip {
  position: absolute;
  bottom: 100%;
  inset-inline-start: 50%;
  -webkit-transform: translateX(-50%) translateY(2px);
  -ms-transform: translateX(-50%) translateY(2px);
  transform: translateX(-50%) translateY(2px);
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  background-color: var(--tj-color-theme-dark);
  color: var(--tj-color-common-white);
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  padding: 4px 6px;
  border-radius: 4px;
  visibility: hidden;
  opacity: 0;
}
.tj-product-details-variation-list button .tj-color-variation-tootltip::before {
  position: absolute;
  content: "";
  bottom: -6px;
  inset-inline-start: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid var(--tj-color-theme-dark);
  -webkit-border-start: 6px solid transparent;
          border-inline-start: 6px solid transparent;
  -webkit-border-end: 6px solid transparent;
          border-inline-end: 6px solid transparent;
}
.tj-product-details-variation-list button.tj-size-variation-btn {
  width: 40px;
  height: 40px;
  border-radius: 0;
}
.tj-product-details-variation-list button.tj-size-variation-btn:hover, .tj-product-details-variation-list button.tj-size-variation-btn.active {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: var(--tj-color-theme-dark);
  box-shadow: none;
  color: var(--tj-color-theme-dark);
}
.tj-product-details-variation-list button:hover, .tj-product-details-variation-list button.active {
  -webkit-box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
          box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
}
.tj-product-details-variation-list button:hover span[data-bg-color], .tj-product-details-variation-list button.active span[data-bg-color] {
  -webkit-transform: translate(-50%, -50%) scale(0.7);
  -ms-transform: translate(-50%, -50%) scale(0.7);
  transform: translate(-50%, -50%) scale(0.7);
}
.tj-product-details-variation-list button:hover .tj-color-variation-tootltip {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateX(-50%) translateY(-6px);
  -ms-transform: translateX(-50%) translateY(-6px);
  transform: translateX(-50%) translateY(-6px);
}
.tj-product-details-wishlist button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 0;
  color: var(--tj-color-theme-dark);
  background-color: var(--tj-color-common-white);
  border: 1px solid var(--tj-color-border-2);
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.tj-product-details-wishlist button::before {
  font-size: 22px;
  content: "\f004";
  font-family: var(--tj-ff-fontawesome);
  margin: 0;
  font-weight: 300;
  color: var(--tj-color-theme-dark);
  display: inline-block;
  -webkit-transform: translate(0px, 0px);
  -ms-transform: translate(0px, 0px);
  transform: translate(0px, 0px);
}
.tj-product-details-wishlist button.woosw-btn.woosw-btn-added::before {
  content: "\f00c";
  margin: 0;
}
.tj-product-details-wishlist button:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.tj-product-details-wishlist button:hover::before {
  color: var(--tj-color-common-white);
}
.tj-product-details-quantity .tj-product-quantity {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  border-radius: 0;
  position: relative;
}
.tj-product-details-quantity .tj-product-quantity .quantity {
  position: relative;
  background: transparent;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border: 1px solid var(--tj-color-border-2);
}
.tj-product-details-quantity .tj-cart-plus, .tj-product-details-quantity .tj-cart-minus {
  display: inline-block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.tj-product-details-quantity .tj-cart-plus:hover, .tj-product-details-quantity .tj-cart-minus:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-quantity .tj-cart-input[type=text] {
  height: 44px;
  line-height: 44px;
  width: 70px;
  background-color: transparent;
  border: 0;
  -webkit-border-end: 1px solid var(--tj-color-border-2);
          border-inline-end: 1px solid var(--tj-color-border-2);
  -webkit-border-start: 1px solid var(--tj-color-border-2);
          border-inline-start: 1px solid var(--tj-color-border-2);
  border-radius: 0px;
  font-size: 16px;
  color: var(--tj-color-theme-dark);
  text-align: center;
}
.tj-product-details-quantity .tj-cart-input[type=text]:focus {
  outline: none;
}
.tj-product-details-add-to-cart .tj-cart-btn {
  width: auto;
  background-color: transparent;
  border: 1px solid var(--tj-color-border-2);
  color: var(--tj-color-theme-dark);
  border-radius: 30px;
}
.tj-product-details-add-to-cart .tj-cart-btn span {
  color: var(--tj-color-theme-dark);
}
.tj-product-details-add-to-cart .tj-cart-btn:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.tj-product-details-add-to-cart .tj-cart-btn:hover span,
.tj-product-details-add-to-cart .tj-cart-btn:hover i {
  color: var(--tj-color-common-white);
}
.tj-product-details-buy-now-btn {
  max-width: 405px;
  display: inline-block;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 30px;
  border-radius: 50px;
  background-color: var(--tj-color-theme-dark);
  color: var(--tj-color-common-white);
}
.tj-product-details-buy-now-btn .btn-text {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  overflow: hidden;
  color: inherit;
  text-shadow: 0 23px 0 currentColor;
}
.tj-product-details-buy-now-btn .btn-text span {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
  -webkit-transition: 0.5s;
  transition: 0.5s;
}
.tj-product-details-buy-now-btn:focus-within, .tj-product-details-buy-now-btn:focus, .tj-product-details-buy-now-btn:hover {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.tj-product-details-buy-now-btn:focus-within .btn-text span, .tj-product-details-buy-now-btn:focus .btn-text span, .tj-product-details-buy-now-btn:hover .btn-text span {
  -webkit-transform: translateY(-24px);
      -ms-transform: translateY(-24px);
          transform: translateY(-24px);
}
.tj-product-details-query {
  margin-top: 30px;
}
.tj-product-details-query-title {
  margin-bottom: 15px;
}
.tj-product-details-query-item:not(:last-child) {
  margin-bottom: 3px;
}
.tj-product-details-query-item > span {
  font-size: 16px;
  font-weight: 600;
  color: var(--tj-color-text-body);
  -webkit-margin-end: 6px;
          margin-inline-end: 6px;
}
.tj-product-details-query-item p {
  font-size: 15px;
  margin-bottom: 0;
}
.tj-product-details-query a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-social {
  margin-bottom: 22px;
  margin-top: 22px;
}
.tj-product-details-social span {
  font-size: 15px;
  color: var(--tj-color-theme-dark);
  -webkit-margin-end: 2px;
          margin-inline-end: 2px;
}
.tj-product-details-social a {
  display: inline-block;
  width: 38px;
  height: 38px;
  line-height: 34px;
  text-align: center;
  border: 1px solid #e6e7e8;
  border-radius: 50%;
}
.tj-product-details-social a:hover {
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
}
.tj-product-details-msg ul li {
  list-style: none;
  position: relative;
  font-size: 15px;
  -webkit-padding-start: 25px;
          padding-inline-start: 25px;
}
.tj-product-details-msg ul li::after {
  position: absolute;
  content: url("../img/product/icons/check-3.svg");
  top: 4px;
  inset-inline-start: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  color: var(--tj-color-common-white);
}
.tj-product-details-payment {
  background-color: #f3f5f6;
  padding: 18px 30px;
}
.tj-product-details-payment p {
  font-size: 16px;
  line-height: 1;
  margin-bottom: 0;
  -webkit-margin-end: 46px;
          margin-inline-end: 46px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tj-product-details-payment p {
    -webkit-margin-end: 25px;
            margin-inline-end: 25px;
  }
}
@media (max-width: 575px) {
  .tj-product-details-payment p {
    -webkit-margin-end: 0;
            margin-inline-end: 0;
    margin-bottom: 15px;
  }
  .tj-product-details-payment p br {
    display: none;
  }
}
.tj-product-details-desc-title {
  font-size: 34px;
  font-weight: 500;
  margin-bottom: 13px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tj-product-details-desc-title {
    font-size: 30px;
  }
}
@media (max-width: 575px) {
  .tj-product-details-desc-title {
    font-size: 26px;
  }
}
.tj-product-details-desc-title-2 {
  font-size: 34px;
  font-weight: 400;
  margin-bottom: 14px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-desc-title-2 {
    font-size: 25px;
  }
}
.tj-product-details-desc-content {
  margin-bottom: 25px;
  -webkit-padding-end: 45px;
          padding-inline-end: 45px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
  .tj-product-details-desc-content {
    -webkit-padding-end: 0;
            padding-inline-end: 0;
  }
}
@media (max-width: 575px) {
  .tj-product-details-desc-content {
    -webkit-padding-start: 0;
            padding-inline-start: 0;
  }
}
.tj-product-details-desc-content span {
  font-size: 20px;
  color: var(--tj-color-theme-dark);
}
.tj-product-details-desc-content p {
  font-size: 16px;
  line-height: 1.6;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-desc-content-2 {
    -webkit-padding-end: 0;
            padding-inline-end: 0;
    -webkit-padding-start: 0;
            padding-inline-start: 0;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-desc-content-2.pt-75 {
    padding-top: 25px;
  }
}
.tj-product-details-desc-content-2 p {
  font-size: 16px;
  line-height: 1.6;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-desc-thumb img {
    max-width: 100%;
  }
}
.tj-product-details-desc-list {
  padding-top: 13px;
}
.tj-product-details-desc-list ul li {
  list-style: none;
  font-size: 16px;
  color: var(--tj-color-theme-dark);
  position: relative;
  -webkit-padding-start: 17px;
          padding-inline-start: 17px;
}
.tj-product-details-desc-list ul li:not(:last-child) {
  margin-bottom: 3px;
}
.tj-product-details-desc-list ul li::after {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  top: 11px;
  width: 4px;
  height: 4px;
  background-color: #a8acb0;
  border-radius: 50%;
}
.tj-product-details-desc-fact-thumb img {
  margin-bottom: 16px;
}
.tj-product-details-desc-fact-content span {
  display: inline-block;
  font-size: 50px;
  line-height: 1.2;
  color: var(--tj-color-theme-dark);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tj-product-details-desc-fact-content span {
    font-size: 35px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tj-product-details-desc-fact-content span {
    font-size: 30px;
  }
}
@media (max-width: 575px) {
  .tj-product-details-desc-fact-content span {
    font-size: 25px;
  }
}
.tj-product-details-desc-fact-content p {
  font-size: 22px;
}
@media (max-width: 575px) {
  .tj-product-details-desc-fact-content p {
    font-size: 20px;
  }
}
.tj-product-details-additional-info {
  padding-top: 60px;
}
@media (max-width: 575px) {
  .tj-product-details-additional-info {
    overflow-x: scroll;
  }
}
.tj-product-details-additional-info-title {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 16px;
  display: none;
}
.tj-product-details-additional-info table {
  border: 1px solid var(--tj-color-border-2);
  width: 100%;
}
.tj-product-details-additional-info table tr:not(:last-child) {
  border-bottom: 1px solid var(--tj-color-border-2);
}
.tj-product-details-additional-info table tr td {
  padding: 12px 34px;
}
.tj-product-details-additional-info table tr td:first-child {
  font-size: 16px;
  color: var(--tj-color-theme-dark);
  background-color: transparent;
  width: 306px;
}
.tj-product-details-additional-info table tr td:last-child {
  font-size: 16px;
  color: var(--tj-color-text-body);
}
.tj-product-details-review-number {
  border: 1px solid #e0e2e3;
  padding: 35px 43px 33px 40px;
}
@media (max-width: 575px) {
  .tj-product-details-review-number {
    padding: 35px 25px 33px 25px;
  }
}
.tj-product-details-review-number-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 14px;
}
.tj-product-details-review-summery {
  margin-bottom: 12px;
}
.tj-product-details-review-summery-value span {
  font-size: 40px;
  font-weight: 500;
  color: var(--tj-color-theme-dark);
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
}
.tj-product-details-review-summery-rating {
  -webkit-margin-end: 3px;
          margin-inline-end: 3px;
}
.tj-product-details-review-summery-rating span {
  color: #ffb21d;
}
.tj-product-details-review-summery-rating p {
  -webkit-margin-start: 4px;
          margin-inline-start: 4px;
  font-size: 14px;
  margin-bottom: 0;
}
.tj-product-details-review-rating-item > span {
  color: #a0a2a4;
  font-size: 15px;
  -webkit-margin-end: 10px;
          margin-inline-end: 10px;
}
.tj-product-details-review-rating-bar {
  width: 260px;
  background-color: #edeeee;
  height: 10px;
  position: relative;
  -webkit-margin-end: 12px;
          margin-inline-end: 12px;
}
@media (max-width: 575px) {
  .tj-product-details-review-rating-bar {
    width: 130px;
  }
}
.tj-product-details-review-rating-bar-inner {
  height: 100%;
  background-color: #ffb21d;
  display: inline-block;
  position: absolute;
  top: 0;
  inset-inline-start: 0;
}
.tj-product-details-review-rating-percent span {
  font-size: 14px;
}
.tj-product-details-review-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 22px;
}
@media (max-width: 575px) {
  .tj-product-details-review-list {
    -webkit-padding-end: 0;
            padding-inline-end: 0;
  }
}
.tj-product-details-review-avater:not(:last-child) {
  margin-bottom: 32px;
}
.tj-product-details-review-avater-thumb img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
}
.tj-product-details-review-avater-rating {
  line-height: 1;
  margin-bottom: 3px;
}
.tj-product-details-review-avater-rating span {
  font-size: 10px;
  -webkit-margin-end: 2px;
          margin-inline-end: 2px;
  color: #ffb21d;
}
.tj-product-details-review-avater-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 9px;
  display: inline-block;
}
.tj-product-details-review-avater-meta {
  font-size: 14px;
  position: relative;
  -webkit-padding-start: 11px;
          padding-inline-start: 11px;
  -webkit-margin-start: 3px;
          margin-inline-start: 3px;
}
.tj-product-details-review-avater-meta::after {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  top: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #a8acb0;
}
.tj-product-details-review-avater-comment p {
  font-size: 14px;
  margin-bottom: 0;
  line-height: 1.4;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-review-form {
    margin-top: 50px;
  }
}
.tj-product-details-review-form > p {
  font-size: 16px;
  margin-bottom: 8px;
}
.tj-product-details-review-form-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 4px;
}
.tj-product-details-review-form-rating {
  margin-bottom: 28px;
}
.tj-product-details-review-form-rating p {
  margin-bottom: 0;
  font-size: 14px;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
}
.tj-product-details-review-form-rating-icon span {
  font-size: 12px;
  color: #ffb21d;
}
.tj-product-details-review-input-wrapper {
  margin-bottom: 11px;
}
.tj-product-details-review-input-box {
  position: relative;
}
.tj-product-details-review-input-box:not(:last-child) {
  margin-bottom: 29px;
}
.tj-product-details-review-input input {
  height: 56px;
  background: #ffffff;
  border: 1px solid #e0e2e3;
  font-size: 14px;
  color: var(--tj-color-theme-dark);
}
.tj-product-details-review-input textarea {
  height: 165px;
  resize: none;
}
.tj-product-details-review-input-title label {
  font-size: 14px;
  color: var(--tj-color-theme-dark);
  position: absolute;
  top: -7px;
  inset-inline-start: 20px;
  padding: 0 5px;
  background-color: var(--tj-color-common-white);
  line-height: 1;
}
.tj-product-details-review-input-eye {
  position: absolute;
  inset-inline-end: 26px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.tj-product-details-review-input-eye .open-eye {
  display: none;
}
.tj-product-details-review-input-eye span {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.tj-product-details-review-input-eye:hover {
  cursor: pointer;
}
.tj-product-details-review-input-eye:hover span {
  color: var(--tj-color-theme-dark);
}
.tj-product-details-review-remeber input {
  display: none;
}
.tj-product-details-review-remeber input:checked ~ label::after {
  background-color: var(--tj-color-theme-primary);
  border-color: var(--tj-color-theme-primary);
}
.tj-product-details-review-remeber input:checked ~ label::before {
  visibility: visible;
  opacity: 1;
}
.tj-product-details-review-remeber label {
  font-size: 15px;
  color: #55585b;
  position: relative;
  -webkit-padding-start: 26px;
          padding-inline-start: 26px;
  z-index: 1;
}
.tj-product-details-review-remeber label::after {
  position: absolute;
  content: "";
  top: 4px;
  inset-inline-start: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  border: 1px solid #c3c7c9;
  z-index: -1;
}
.tj-product-details-review-remeber label::before {
  position: absolute;
  content: url("../img/product/icons/check.svg");
  top: 4px;
  inset-inline-start: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  visibility: hidden;
  opacity: 0;
  color: var(--tj-color-common-white);
}
.tj-product-details-review-remeber label a:hover {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-review-remeber label:hover {
  cursor: pointer;
}
.tj-product-details-review-btn {
  font-size: 16px;
  font-weight: 500;
  color: var(--tj-color-common-white);
  padding: 9px 45px;
  background-color: var(--tj-color-theme-primary);
}
.tj-product-details-tab-nav .tj-product-tab {
  border: 0;
  border-bottom: 1px solid var(--tj-color-border-2);
  gap: 120px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tj-product-details-tab-nav .tj-product-tab {
    gap: 90px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tj-product-details-tab-nav .tj-product-tab {
    gap: 50px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-tab-nav .tj-product-tab {
    gap: 20px;
  }
}
.tj-product-details-tab-nav .tj-product-tab .nav-link {
  font-size: 16px;
  font-weight: 700;
  padding: 0;
  border: 0;
  padding-bottom: 10px;
  color: var(--tj-color-theme-dark);
  background-color: transparent;
  white-space: nowrap;
  position: relative;
}
.tj-product-details-tab-nav .tj-product-tab .nav-link::after {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  bottom: 0;
  width: 0;
  height: 3px;
  background-color: var(--tj-color-theme-dark);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.tj-product-details-tab-nav .tj-product-tab .nav-link.active::after {
  opacity: 1;
  visibility: visible;
  width: 100%;
}
@media (max-width: 575px) {
  .tj-product-details-tab-nav .tj-product-tab span#productTabMarker {
    display: none !important;
  }
}
.tj-product-details-tab-nav .product-tab-content {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.tj-product-details-tab-line {
  position: absolute;
  bottom: 0;
  height: 1px;
  background-color: var(--tj-color-theme-dark);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  display: block;
}
.tj-product-details-views {
  margin-bottom: 10px;
}
.tj-product-details-views > span {
  color: var(--tj-color-theme-dark);
  font-size: 16px;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
}
.tj-product-details-views p {
  font-size: 16px;
  margin-bottom: 0;
}
.tj-product-details-views p span {
  color: var(--tj-color-theme-dark);
  font-weight: 500;
}
.tj-product-details-stock-bar p {
  font-size: 15px;
  margin-bottom: 5px;
}
.tj-product-details-stock-bar p span {
  font-weight: 500;
  color: var(--tj-color-theme-dark);
}
.tj-product-details-stock-bar-line {
  height: 4px;
  position: relative;
}
.tj-product-details-stock-bar-line-inner {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  background-color: var(--tj-color-theme-primary);
  height: 100%;
  display: inline-block;
}
.tj-product-details-wishlist-btn {
  display: inline-block;
  width: 46px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  background-color: var(--tj-color-common-white);
  -webkit-box-shadow: 0px 1px 3px rgba(1, 15, 28, 0.1);
          box-shadow: 0px 1px 3px rgba(1, 15, 28, 0.1);
}
.tj-product-details-wishlist-btn:hover {
  background-color: var(--tj-color-theme-dark);
  color: var(--tj-color-common-white);
}
.tj-product-details-nav-main-thumb {
  position: relative;
}
.tj-product-details-thumb-wrapper {
  position: relative;
}
.tj-product-details-thumb-wrapper .nav-tabs {
  -webkit-margin-end: 10px;
          margin-inline-end: 10px;
}
.tj-product-details-thumb-wrapper .nav-tabs .nav-link {
  width: 78px;
  height: 100px;
  position: relative;
}
@media (max-width: 575px) {
  .tj-product-details-thumb-wrapper .nav-tabs .nav-link {
    -webkit-margin-end: 10px;
            margin-inline-end: 10px;
    margin-bottom: 10px;
  }
}
.tj-product-details-thumb-wrapper .nav-tabs .nav-link:not(:last-child) {
  margin-bottom: 10px;
}
.tj-product-details-thumb-wrapper .nav-tabs .nav-link::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background-color: transparent;
  border: 1px solid transparent;
  top: 0;
  inset-inline-start: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.tj-product-details-thumb-wrapper .nav-tabs .nav-link.active::after, .tj-product-details-thumb-wrapper .nav-tabs .nav-link:hover::after {
  border-color: var(--tj-color-theme-dark);
}
.tj-product-details-thumb-wrapper .nav-tabs .nav-link img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.tj-product-details-thumb-wrapper .nav-tabs .nav-link .nav-video-btn {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  width: 24px;
  height: 24px;
  background-color: var(--tj-color-theme-dark);
  color: var(--tj-color-common-white);
  font-size: 15px;
}
.tj-product-details-thumb-slider {
  position: sticky;
  top: 120px;
}
.tj-product-details-thumb-slider:hover .tj-product-details-thumb-arrow button {
  visibility: visible;
  opacity: 1;
  inset-inline-start: 20px;
}
.tj-product-details-thumb-slider:hover .tj-product-details-thumb-arrow button.tj-product-details-thumb-slider-5-button-next {
  inset-inline-start: auto;
  inset-inline-end: 20px;
}
.tj-product-details-thumb-arrow button {
  position: absolute;
  top: 50%;
  inset-inline-start: 0px;
  z-index: 1;
  width: 40px;
  height: 40px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
  margin: 0 5px;
  background-color: var(--tj-color-common-white);
  color: var(--tj-color-theme-dark);
  -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.01);
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.01);
  visibility: hidden;
  opacity: 0;
}
.tj-product-details-thumb-arrow button.tj-product-details-thumb-slider-5-button-next {
  inset-inline-start: auto;
  inset-inline-end: 0;
}
.tj-product-details-thumb-arrow button:hover {
  background-color: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
  -webkit-box-shadow: none;
          box-shadow: none;
}
.tj-product-details-thumb-video-btn {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: var(--tj-color-theme-dark);
  border-radius: 50%;
  display: inline-block;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  background-color: var(--tj-color-common-white);
}
.tj-product-details-thumb-video-btn:hover {
  color: var(--tj-color-common-white);
  background-color: var(--tj-color-theme-primary);
}
.tj-product-details-thumb-gallery-item img {
  width: 100%;
}

.tj-product-details-thumb-wrapper {
  overflow: hidden;
}
.tj-product-details-thumb-wrapper.has_gallery_thumb .tj-product-badge {
  inset-inline-start: 145px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .tj-product-details-thumb-wrapper.has_gallery_thumb .tj-product-badge {
    inset-inline-start: 20px;
  }
}
.tj-product-details-thumb-wrapper.has_gallery_thumb .woocommerce-product-gallery {
  -webkit-padding-start: 125px;
          padding-inline-start: 125px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-thumb-wrapper.has_gallery_thumb .woocommerce-product-gallery {
    padding: 0;
  }
}
.tj-product-details-thumb-wrapper.has_gallery_thumb a.woocommerce-product-gallery__trigger {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  z-index: 2;
}
.tj-product-details-thumb-wrapper.has_gallery_thumb a.woocommerce-product-gallery__trigger::before {
  position: absolute;
  top: 20px;
  inset-inline-end: 20px;
  content: "\e929";
  font-size: 20px;
  font-family: "solvior-icons" !important;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--tj-color-common-white);
  color: var(--tj-color-text-body);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.tj-product-details-thumb-wrapper.has_gallery_thumb a.woocommerce-product-gallery__trigger span {
  display: none;
}
.tj-product-details-thumb-wrapper.has_no_gallery a.woocommerce-product-gallery__trigger {
  display: none;
}
.tj-product-details-thumb-wrapper.has_no_gallery .woocommerce-product-gallery.no_gallery {
  width: 100%;
}
.tj-product-details-thumb-wrapper .woocommerce-product-gallery__image {
  background: var(--tj-color-common-white);
}
.tj-product-details-thumb-wrapper .flex-control-nav.flex-control-thumbs {
  list-style: none;
  -webkit-padding-start: 0;
          padding-inline-start: 0;
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  overflow-y: auto;
  height: 100%;
  margin-bottom: 0;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-thumb-wrapper .flex-control-nav.flex-control-thumbs {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 10px;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-top: 30px;
  }
}
.tj-product-details-thumb-wrapper .flex-control-nav.flex-control-thumbs li {
  background: rgba(128, 128, 128, 0.09);
  cursor: pointer;
}
.tj-product-details-thumb-wrapper .flex-control-nav.flex-control-thumbs li:not(:last-child) {
  margin-bottom: 15px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .tj-product-details-thumb-wrapper .flex-control-nav.flex-control-thumbs li:not(:last-child) {
    margin-bottom: 0;
  }
}
.tj-product-details-thumb-wrapper figure div {
  height: 605px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tj-product-details-thumb-wrapper figure div {
    height: 590px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-thumb-wrapper figure div {
    height: auto;
  }
}
.tj-product-details-thumb-wrapper figure div a {
  height: 100%;
}
.tj-product-details-thumb-wrapper figure div img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

@media (max-width: 390px) {
  .tj-product-details-action-item-wrapper {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.related-products.has-border {
  position: relative;
  padding-top: 120px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .related-products.has-border {
    padding-top: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .related-products.has-border {
    padding-top: 80px;
  }
}
.related-products.has-border::before {
  position: absolute;
  content: "";
  top: 0;
  inset-inline-start: -100%;
  inset-inline-end: -100%;
  height: 1px;
  width: 6000px;
  background-color: var(--tj-color-border-2);
}

.tj-product-details-share {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 7px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-top: 25px;
  margin-top: 25px;
  border-top: 1px solid var(--tj-color-border-2);
}
.tj-product-details-share h6 {
  margin: 0;
}
.tj-product-details-share a {
  font-size: 14px;
  width: 28px;
  height: 28px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: 1;
  color: var(--tj-color-common-white);
  background: #aeb2b9;
  border-radius: 100%;
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
}
.tj-product-details-share a:hover {
  background: var(--tj-color-theme-primary);
  color: var(--tj-color-common-white);
  -webkit-transform: translateY(-5px);
      -ms-transform: translateY(-5px);
          transform: translateY(-5px);
}
.tj-product-details-share a:hover i {
  color: var(--tj-color-common-white);
}

.tj-product-area {
  overflow: hidden;
}

.tj-product-details-thumb-wrapper .tj-product-action-btn a {
  position: absolute;
  right: 15px;
  top: 15px;
  z-index: -1;
  opacity: 0;
}
.tj-product-details-thumb-wrapper .tj-product-action-btn a:first-child {
  z-index: 2;
  opacity: 1;
}
.tj-product-details-thumb-wrapper .tj-product-action-btn a i {
  font-size: 20px;
}
.tj-product-details-thumb-wrapper .product-img {
  height: 605px;
}
.tj-product-details-thumb-wrapper .product-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tj-product-details-thumb-wrapper .product-img {
    height: 590px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-details-thumb-wrapper .product-img {
    height: auto;
  }
}

.tj-product-thumb-items {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 15px;
  -webkit-margin-end: 25px;
          margin-inline-end: 25px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-product-thumb-items {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-margin-end: 0;
            margin-inline-end: 0;
    margin-top: 30px;
    gap: 10px;
  }
}

.tj-pdt-thumb-img {
  width: 100px;
  max-height: 100px;
  background: transparent;
  padding: 0;
}
.tj-pdt-thumb-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .tj-pdt-thumb-img {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
  }
}

.tj-product-img-wrap {
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tj-product-img-wrap {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
  }
}

.tj-product-details-description .review__author {
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: var(--tj-color-heading-primary);
  position: relative;
  text-transform: capitalize;
}
.tj-product-details-description .review__published-date {
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: #888888;
}
.tj-product-details-description ol.commentlist .review .comment_container img {
  width: 60px;
  height: 60px;
}
.tj-product-details-description .star-ratings {
  -webkit-text-stroke: 1px var(--tj-color-theme-primary);
  margin: 5px 0;
  letter-spacing: 4px;
  font-size: 20px;
  cursor: default;
}
.tj-product-details-description .star-ratings span {
  font-size: 20px;
}
.tj-product-details-description .star-ratings .fill-ratings {
  color: var(--tj-color-theme-primary);
  z-index: 5;
}
.tj-product-details-description .star-ratings .fill-ratings span {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-description .star-ratings .empty-ratings {
  color: var(--tj-color-theme-primary);
}
.tj-product-details-description .star-ratings .empty-ratings span {
  color: var(--tj-color-common-white);
}
.tj-product-details-description .comment-notes {
  margin-bottom: 20px;
}
.tj-product-details-description .comment-check {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0;
  margin-top: -15px;
  margin-bottom: 20px;
}
.tj-product-details-description .comment-check input[type=checkbox] {
  margin-right: 10px;
}

.vbox-child {
  background-color: transparent !important;
}

/*----------------------------------------*/
/*  Wishlist START
/*----------------------------------------*/
.woosw-list table {
  border-color: var(--tj-color-border-2);
}
.woosw-list .woosw-items {
  width: 100%;
}
.woosw-list .woosw-items .woosw-item .woosw-item--remove,
.woosw-list .woosw-items .woosw-item .woosw-item--add {
  width: 14px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 14px;
          flex: 0 0 14px;
  font-size: 0;
}
.woosw-list .woosw-items .woosw-item .woosw-item--remove span,
.woosw-list .woosw-items .woosw-item .woosw-item--add span {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  cursor: pointer;
  border: 1px solid var(--tj-color-border-2);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  font-size: 22px;
  line-height: 1;
  color: var(--tj-color-heading-primary);
}
.woosw-list .woosw-items .woosw-item .woosw-item--image img {
  width: 100px;
  -o-object-fit: cover;
     object-fit: cover;
}
.woosw-list .woosw-items .woosw-item .woosw-item--image a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.woosw-list .woosw-items .woosw-item .woosw-item--actions p {
  margin: 0;
}
.woosw-list .woosw-items .woosw-item .stock {
  margin: 0;
  color: var(--tj-color-theme-primary);
}
.woosw-list .woosw-items .woosw-item .stock.out-of-stock {
  color: #ff0004;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .woosw-list .woosw-items .woosw-item .woosw-item--remove,
  .woosw-list .woosw-items .woosw-item .woosw-item--add {
    width: 100%;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
}/*# sourceMappingURL=shop.css.map */