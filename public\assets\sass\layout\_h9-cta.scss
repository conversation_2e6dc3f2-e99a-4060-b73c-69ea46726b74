@use "../utilities" as *;

/* START: Home 9 CTA CSS */
.h9-cta {
	&-section {
		position: relative;
		overflow: hidden;
	}
	&-inner {
		padding: 43px 0 44px;
		&::after {
			content: "";
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			inset-inline-start: 0;
			background-color: var(--tj-color-theme-primary);
			mix-blend-mode: multiply;
		}
	}
	&-wrapper {
		display: flex;
		gap: 45px;
		align-items: center;
		justify-content: flex-end;
		padding-inline-start: 0;
		z-index: 2;
		position: relative;
		@media #{$xxl,$xl} {
			gap: 15px;
		}
		@media #{$lg,$md} {
			justify-content: center;
		}
		@media #{ $sm,$xs} {
			justify-content: center;
			flex-direction: column;
			gap: 20px;
		}

		.newsletter-title {
			max-width: 370px;
			width: 100%;
			@media #{$xl,$lg} {
				max-width: 280px;
			}
			@media #{$md} {
				max-width: 303px;
			}
			.title {
				font-size: 32px;
				font-weight: var(--tj-fw-sbold);
				color: var(--tj-color-common-white);
				letter-spacing: -0.025em;
				margin-bottom: 0;

				@media #{$md,$sm,$xs} {
					text-align: center;
				}
			}
		}

		.newsletter-form {
			max-width: 400px;

			width: 100%;
			@media #{$xl,$lg} {
				max-width: 370px;
			}
			.form-input {
				position: relative;
				z-index: 2;
				input:not([type="submit"]):not([type="radio"]):not([type="checkbox"]),
				.footer-widget .newsletter-form .form-input input[type="email"] {
					color: var(--tj-color-heading-primary);
					font-weight: var(--tj-fw-regular);
					font-size: 16px;
					padding: 18px 80px 18px 25px;
					background: var(--tj-color-common-white);
					border-radius: 70px;
					&::placeholder {
						color: var(--tj-color-common-black-2);
						font-weight: var(--tj-fw-regular);
						font-size: 16px;
					}

					&:focus {
						border-color: var(--tj-color-theme-primary);
						color: var(--tj-color-theme-primary);
					}
				}
			}
			@media #{ $xs} {
				max-width: 100%;
			}

			.tj-primary-btn {
				position: absolute;
				inset-inline-end: 4px;
				top: 50%;
				transform: translateY(-50%);
				.btn_inner {
					padding: 15px 43px 15px 20px;
					&::before {
						width: 100%;
						background-color: var(--tj-color-theme-dark);
					}
				}
				.btn_icon {
					inset-inline-start: auto;
					inset-inline-end: 0;
					height: 40px;
				}
			}
		}
	}
}
