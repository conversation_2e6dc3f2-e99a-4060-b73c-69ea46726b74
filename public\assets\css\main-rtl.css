/*START: Default CSS */
select,
.nice-select,
input:not([type=radio]):not([type=checkbox]),
textarea {
  direction: rtl;
}

.tj-primary-btn .btn_inner {
  padding: 15px 55px 15px 20px;
}
.tj-primary-btn .btn_inner .btn_icon {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
@media (max-width: 575px) {
  .tj-primary-btn .btn_inner {
    padding: 12px 50px 12px 18px;
  }
}

.tji-play {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.text-btn i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.text-btn::before {
  -webkit-transform-origin: left;
      -ms-transform-origin: left;
          transform-origin: left;
}
.text-btn:hover::before {
  -webkit-transform-origin: right;
      -ms-transform-origin: right;
          transform-origin: right;
}
.text-btn-2 i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.icon-btn {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tji-double-check {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

#loading-icon {
  -webkit-transform: translate(50%, -50%) scaleX(-1);
      -ms-transform: translate(50%, -50%) scaleX(-1);
          transform: translate(50%, -50%) scaleX(-1);
}

.hover\:shine::before {
  inset-inline-start: auto;
  inset-inline-end: -140%;
}

.swiper_navigations {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.cursor-outer {
  -webkit-margin-start: auto;
          margin-inline-start: auto;
  -webkit-margin-end: -15px;
          margin-inline-end: -15px;
}

.cursor-inner {
  -webkit-margin-start: auto;
          margin-inline-start: auto;
  -webkit-margin-end: -3px;
          margin-inline-end: -3px;
}

.tj-pagination ul li .page-numbers i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
  display: inline-block;
}

.odometer {
  direction: rtl;
  unicode-bidi: isolate;
  text-align: right;
}

.odometer-inside {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

/* !END:  Default CSS */
/*START: Header CSS */
.topbar_note a i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tj-primary-btn.header_btn .btn_inner {
  padding: 12px 50px 12px 18px;
}

.tj-header-area .tj-primary-btn .btn_inner {
  padding: 12px 50px 12px 18px !important;
}

.tj_search_wrapper .search_form form .search_input .search-box input[type=search] {
  padding: 20px 24px 20px 75px;
}

.mainmenu ul > li > .mega-menu-service-single .mega-menu-service-nav,
.mobile_menu ul > li > .mega-menu-service-single .mega-menu-service-nav {
  -webkit-transform: rotate(-135deg) translate(22px, -19px);
      -ms-transform: rotate(-135deg) translate(22px, -19px);
          transform: rotate(-135deg) translate(22px, -19px);
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
  .mainmenu ul > li > .mega-menu-service-single .mega-menu-service-nav,
  .mobile_menu ul > li > .mega-menu-service-single .mega-menu-service-nav {
    -webkit-transform: rotate(-135deg) translate(58px, -73px);
        -ms-transform: rotate(-135deg) translate(58px, -73px);
            transform: rotate(-135deg) translate(58px, -73px);
  }
}
@media (max-width: 575px) {
  .mainmenu ul > li > .mega-menu-service-single .mega-menu-service-nav,
  .mobile_menu ul > li > .mega-menu-service-single .mega-menu-service-nav {
    -webkit-transform: rotate(-135deg) translate(-7px, -17px);
        -ms-transform: rotate(-135deg) translate(-7px, -17px);
            transform: rotate(-135deg) translate(-7px, -17px);
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .hamburger_menu .mean-container .mean-nav ul li .mega-menu-service a {
    text-align: start;
  }
}

/* !END: Header  CSS */
/*START: Hero CSS */
.hero-content .hero-shapes-1 img {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.hero-circle {
  -webkit-transform: translateY(-50%) scaleX(-1);
      -ms-transform: translateY(-50%) scaleX(-1);
          transform: translateY(-50%) scaleX(-1);
}
.hero-circle .circle-wrap {
  inset-inline-start: 15px;
  inset-inline-end: auto;
}
.hero-circle .circle-wrap .rotate-image {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

.hero_scroll .down {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.tj-slider-section::before {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tj-hero-slider.style-1 .tj-btn {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tj-hero-section-two .hero_shapes {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.hero-images-box-two .hero-button::before {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}
.hero-images-box-two .hero-button .hero-btn::before {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

.hero-three-bg {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.hero-funfact .circle-wrap a {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}
.hero-funfact .circle-wrap a i {
  -webkit-transform: rotate(-135deg);
      -ms-transform: rotate(-135deg);
          transform: rotate(-135deg);
}
.hero-funfact .circle-wrap a:hover i {
  -webkit-transform: rotate(-135deg) scale(1.5);
      -ms-transform: rotate(-135deg) scale(1.5);
          transform: rotate(-135deg) scale(1.5);
}

.hero-images-box-three {
  -webkit-clip-path: polygon(92% 0, 0 0, 0 100%, 100% 100%, 100% 8%);
          clip-path: polygon(92% 0, 0 0, 0 100%, 100% 100%, 100% 8%);
}

.hero-funfact {
  -webkit-clip-path: polygon(95% 0, 0 0, 0 85%, 5% 100%, 100% 100%, 100% 16%);
          clip-path: polygon(95% 0, 0 0, 0 85%, 5% 100%, 100% 100%, 100% 16%);
}

.hero-video-btn .video-btn {
  padding: 16px 20px 16px 25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
  .hero-video-btn .video-btn {
    padding: 12px 14px 12px 20px;
  }
}

.h7-hero-slider.tj-hero-slider.style-1 .tj-btn i {
  -webkit-transform: scale(-1);
      -ms-transform: scale(-1);
          transform: scale(-1);
}

.h7-hero-slider .tj-hero-shape {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.h7-hero-slider .tj-hero-shape::after {
  inset-inline-start: 19px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .h7-hero-slider .tj-hero-shape::after {
    inset-inline-start: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .h7-hero-slider .tj-hero-shape::after {
    inset-inline-start: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .h7-hero-slider .tj-hero-shape::after {
    inset-inline-start: 5px;
  }
}
@media (max-width: 575px) {
  .h7-hero-slider .tj-hero-shape::after {
    inset-inline-start: 2px;
  }
}

.h8-hero-circle .circle-wrap {
  inset-inline-end: 15px !important;
  inset-inline-start: auto !important;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .h8-hero-circle .circle-wrap {
    inset-inline-end: 55px !important;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h8-hero-circle .circle-wrap {
    inset-inline-end: 25px !important;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h8-hero-circle {
    -webkit-transform: none !important;
        -ms-transform: none !important;
            transform: none !important;
  }
}

.h9-hero-chart-wrapper {
  -webkit-transform: translateX(-26%) rotate(8deg);
      -ms-transform: translateX(-26%) rotate(8deg);
          transform: translateX(-26%) rotate(8deg);
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .h9-hero-chart-wrapper {
    -webkit-transform: translateX(42%) rotate(8deg);
        -ms-transform: translateX(42%) rotate(8deg);
            transform: translateX(42%) rotate(8deg);
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .h9-hero-chart-wrapper {
    -webkit-transform: translateX(28%) rotate(8deg);
        -ms-transform: translateX(28%) rotate(8deg);
            transform: translateX(28%) rotate(8deg);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .h9-hero-chart-wrapper {
    -webkit-transform: translateX(-5%) rotate(8deg);
        -ms-transform: translateX(-5%) rotate(8deg);
            transform: translateX(-5%) rotate(8deg);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .h9-hero-chart-wrapper {
    -webkit-transform: translateX(28%) rotate(8deg);
        -ms-transform: translateX(28%) rotate(8deg);
            transform: translateX(28%) rotate(8deg);
  }
}
@media (max-width: 575px) {
  .h9-hero-chart-wrapper {
    -webkit-transform: translateX(28%) rotate(8deg);
        -ms-transform: translateX(28%) rotate(8deg);
            transform: translateX(28%) rotate(8deg);
  }
}

.h10-hero-content {
  padding: 232px 12px 80px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .h10-hero-content {
    padding: 200px 12px 70px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .h10-hero-content {
    padding: 110px 0 70px;
  }
}
@media (max-width: 575px) {
  .h10-hero-content {
    padding: 140px 0 70px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h10-hero-stat-inner {
    padding: 20px 15px 30px 10px;
  }
}
.h10-hero-stat-chart-text {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h10-hero-stat {
    -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}

/* !END:  hero CSS */
/*START: About CSS */
.tj-about-section::before {
  border-radius: 0 0 0 var(--br-bottom-left);
}

.tj-about-section .about-bg-images .about-shape-1 {
  -webkit-transform: translateY(-50%) scaleX(-1);
      -ms-transform: translateY(-50%) scaleX(-1);
          transform: translateY(-50%) scaleX(-1);
  text-align: end;
}

.funfact-item-two {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.funfact-item-two .funfact-box {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.about-image-wrap .circle-wrap .rotate-image {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

.about-circle {
  -webkit-transform: translateY(-50%) scaleX(-1);
      -ms-transform: translateY(-50%) scaleX(-1);
          transform: translateY(-50%) scaleX(-1);
}
.about-circle .circle-wrap {
  inset-inline-start: 15px;
  inset-inline-end: auto;
}
.about-circle .circle-wrap .rotate-image {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

.h6-about-video .about-shape-1 .video-inner .video-btn {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

.h7-about-section .about-float-area-left {
  padding: 48px 50px 0 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .h7-about-section .about-float-area-left {
    padding: 40px 0 0 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h7-about-section .about-float-area-left {
    padding: 0;
  }
}

.h9-about-circle .circle-wrap {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

.tj-evolute {
  -webkit-clip-path: polygon(0 0, 0 90%, 10% 100%, 100% 100%, 100% 0);
          clip-path: polygon(0 0, 0 90%, 10% 100%, 100% 100%, 100% 0);
}

.tj-evolute-image {
  -webkit-clip-path: polygon(0 0, 0 100%, 100% 100%, 100% 10%, 90% 0);
          clip-path: polygon(0 0, 0 100%, 100% 100%, 100% 10%, 90% 0);
}

/* !END:  About CSS */
/*START: Service CSS */
.tj-service-section .sec-heading .service-rating .star-fill {
  padding: 2px 12px 3px 7px;
}

.service-item .service-content {
  padding: 60px 65px 60px 15px;
}
.service-item .service-content .service-text .service-icons img {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-item .service-content {
    padding: 40px 30px 40px 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .service-item .service-content {
    padding: 35px 15px 50px;
  }
}

.tj-service-section-three .sec-heading .service-rating .star-fill {
  padding: 2px 12px 3px 7px;
}

.service-style-4 .service-button {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.blog-btn-2 .btn-icon {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.blog-btn-2 .btn-text {
  -webkit-transform: translateX(20px);
      -ms-transform: translateX(20px);
          transform: translateX(20px);
}

.h6-services-item-inner .service_content {
  padding: 35px 30px 35px 0;
}
.h6-services-item-inner .service_shape {
  -webkit-mask-image: url(../images/shapes/h6-service-rtl.svg);
          mask-image: url(../images/shapes/h6-service-rtl.svg);
}

.h6-services-item .h6-services-item-inner .service_content .service_btn .btn-icon {
  -webkit-transform: rotate(-135deg);
      -ms-transform: rotate(-135deg);
          transform: rotate(-135deg);
}
.h6-services-item .h6-services-item-inner .service_content .service_btn .btn-text {
  -webkit-transform: translateX(20px);
      -ms-transform: translateX(20px);
          transform: translateX(20px);
}
.h6-services-item .h6-services-item-inner .service_content .service_btn:hover .btn-text {
  -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
          transform: translateX(0);
}
.h6-services-item:hover .h6-services-item-inner .service_content .service_btn .btn-icon {
  -webkit-transform: rotate(-180deg);
      -ms-transform: rotate(-180deg);
          transform: rotate(-180deg);
}

.h7-service-section .service-style-3 .service-button {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
@media (max-width: 575px) {
  .h7-service-section .service-style-3 .service-button {
    -webkit-transform: rotate(-90deg) scaleX(-1);
        -ms-transform: rotate(-90deg) scaleX(-1);
            transform: rotate(-90deg) scaleX(-1);
    -webkit-margin-end: 0;
            margin-inline-end: 0;
  }
}
.h7-service-section .service-style-3 .blog-btn-2 .btn-icon {
  -webkit-margin-end: auto;
          margin-inline-end: auto;
  -webkit-margin-start: 13px;
          margin-inline-start: 13px;
}

.h8-video .play_btn {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

.h9-services-item .service-btn:hover .text-btn::before {
  -webkit-transform-origin: right;
      -ms-transform-origin: right;
          transform-origin: right;
}

.service-category ul li a i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tj-sidebar-cta .cta-btn .shapes {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tj-post-thumb a.play-btn {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}
.tj-post-thumb a.play-btn i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

/* !END: Service CSS */
/*START: Testimonials CSS */
.testimonial-item {
  padding: 35px 0px 40px 40px;
}
.testimonial-item .tj-testimonial-author .author-images {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.testimonial-item .tj-testimonial-author .author-images img {
  inset-inline-end: auto;
  inset-inline-start: 10px;
}
.testimonial-item .testimonial-content .testimonial-quote i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .testimonial-item {
    padding: 35px 0px 40px 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .testimonial-item {
    padding: 35px 0px 40px 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
  .testimonial-item {
    padding: 35px 0px 40px 15px;
  }
}
@media (max-width: 575px) {
  .testimonial-item {
    padding: 30px 0px 35px 15px;
  }
}

.testimonial-style-2 .testimonial-content .testimonial-quote i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
  display: inline-block;
}

.tj-testimonial-slider-two .shape-image {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.testimonial-style-3 .testimonial-infos .testimonial-quote i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.testimonial-style-3 .testimonial-content-box {
  padding: 55px 110px 35px 30px;
}
@media (max-width: 575px) {
  .testimonial-style-3 .testimonial-content-box {
    padding: 40px 120px 35px 20px;
  }
}
.testimonial-style-3 .testimonial-content-box .testimonial-content .testimonial-author .testimonial-rating {
  padding: 2px 13px 4px 7px;
}

.h5-testimonial-item .quote-icon i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
  display: inline-block;
}

.h6-testimonial-quote {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.h7-testimonial-single .h7-testimonial-title {
  -webkit-transform: translateX(50%);
      -ms-transform: translateX(50%);
          transform: translateX(50%);
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h7-testimonial-single .h7-testimonial-title {
    -webkit-transform: translateY(50%);
        -ms-transform: translateY(50%);
            transform: translateY(50%);
  }
}

.h8-testimonial-item {
  padding: 35px 30px 35px 30px;
}
@media (max-width: 575px) {
  .h8-testimonial-item {
    padding: 30px 20px;
  }
}

.h9-testimonial-wrapper .slider-prev {
  -webkit-margin-start: auto;
          margin-inline-start: auto;
  -webkit-margin-end: -90px;
          margin-inline-end: -90px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .h9-testimonial-wrapper .slider-prev {
    -webkit-margin-start: auto;
            margin-inline-start: auto;
    -webkit-margin-end: -25px;
            margin-inline-end: -25px;
  }
}

.h9-testimonial-wrapper .slider-next {
  -webkit-margin-end: auto;
          margin-inline-end: auto;
  -webkit-margin-start: -90px;
          margin-inline-start: -90px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .h9-testimonial-wrapper .slider-next {
    -webkit-margin-end: auto;
            margin-inline-end: auto;
    -webkit-margin-start: -25px;
            margin-inline-start: -25px;
  }
}

/* !END: Testimonials CSS */
/*START: Projects CSS */
.tj-cursor {
  inset-inline-start: auto;
  inset-inline-end: 0;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.project-item .project-content .project-title .title a i {
  top: 2px;
  -webkit-transform: translateX(20px) scaleX(-1);
      -ms-transform: translateX(20px) scaleX(-1);
          transform: translateX(20px) scaleX(-1);
}
.project-item:hover .project-content .project-title .title a i {
  -webkit-transform: translateX(0) scaleX(-1);
      -ms-transform: translateX(0) scaleX(-1);
          transform: translateX(0) scaleX(-1);
}

.project-style-2 .project-images a {
  -webkit-transform: translate(50%, -50%) scale(0.5);
      -ms-transform: translate(50%, -50%) scale(0.5);
          transform: translate(50%, -50%) scale(0.5);
}
.project-style-2:hover .project-images a {
  -webkit-transform: translate(50%, -50%) scale(1);
      -ms-transform: translate(50%, -50%) scale(1);
          transform: translate(50%, -50%) scale(1);
}

.project-style-3 .project-thumb .project-default-content span {
  -webkit-transform: translateX(50%) rotate(-135deg);
      -ms-transform: translateX(50%) rotate(-135deg);
          transform: translateX(50%) rotate(-135deg);
}
.project-style-3 .project-thumb .project-default-content .title {
  -webkit-transform: translateX(-100%) rotate(180deg);
      -ms-transform: translateX(-100%) rotate(180deg);
          transform: translateX(-100%) rotate(180deg);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
  .project-style-3 .project-thumb .project-default-content .title {
    -webkit-transform: translateX(-77%) rotate(180deg);
        -ms-transform: translateX(-77%) rotate(180deg);
            transform: translateX(-77%) rotate(180deg);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .project-style-3 .project-thumb .project-default-content span {
    -webkit-transform: scaleX(-1) !important;
        -ms-transform: scaleX(-1) !important;
            transform: scaleX(-1) !important;
  }
}

.project-cursor.cursor-big {
  inset-inline-start: auto;
  inset-inline-end: -85px;
}
.project-cursor span {
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}

.h5-case-study-item .case-study-content {
  padding: 30px 30px 30px 22px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h5-case-study-item .case-study-content {
    padding: 30px 20px;
  }
}

.h7-project-inner .tj-project-nav i {
  -webkit-transform: scale(-1);
      -ms-transform: scale(-1);
          transform: scale(-1);
}

.h8-case-study-item .h8-case-study-banner .icon-btn {
  -webkit-transform: translate(50%, -50%) scale(0.5);
      -ms-transform: translate(50%, -50%) scale(0.5);
          transform: translate(50%, -50%) scale(0.5);
}
.h8-case-study-item .h8-case-study-banner .icon-btn i {
  -webkit-transform: rotate(-135deg) translateX(0);
      -ms-transform: rotate(-135deg) translateX(0);
          transform: rotate(-135deg) translateX(0);
}
.h8-case-study-item .h8-case-study-banner .icon-btn:hover i {
  -webkit-transform: rotate(-135deg) translateX(56px);
      -ms-transform: rotate(-135deg) translateX(56px);
          transform: rotate(-135deg) translateX(56px);
}
.h8-case-study-item:hover .h8-case-study-banner .icon-btn {
  -webkit-transform: translate(50%, -50%) scale(1);
      -ms-transform: translate(50%, -50%) scale(1);
          transform: translate(50%, -50%) scale(1);
}

.h9-case-study-item .case-study-content {
  padding: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .h9-case-study-item .case-study-content {
    padding: 20px 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .h9-case-study-item .case-study-content {
    padding: 20px 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .h9-case-study-item .case-study-content {
    padding: 20px 10px;
  }
}
@media (max-width: 575px) {
  .h9-case-study-item .case-study-content {
    padding: 15px 15px;
  }
}

/* !END: Projects CSS */
/*START: Funfact CSS CSS */
.counter-item {
  padding: 50px 55px 60px 40px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .counter-item {
    padding: 50px 31px 60px 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .counter-item {
    padding: 30px 20px 35px 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .counter-item {
    padding: 30px 70px 35px 15px;
  }
}
@media (max-width: 575px) {
  .counter-item {
    padding: 30px 15px;
  }
}

/* !END: Funfact CSS */
/*START: Brand Slider CSS */
.brand-slider-1::before, .brand-slider-1::after, .brand-slider-2::before, .brand-slider-2::after {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

/* !END: Brand Slider CSS */
/*START: Contact CSS */
.form-button button[type=submit] i,
.form-button button[type=submit] svg,
.form-button button[type=submit] .form-button input[type=submit] i,
.form-button button[type=submit] svg {
  -webkit-transform: scaleX(-1) rotate(0);
      -ms-transform: scaleX(-1) rotate(0);
          transform: scaleX(-1) rotate(0);
}
.form-button button[type=submit]:hover i,
.form-button button[type=submit]:hover svg,
.form-button button[type=submit]:hover .form-button input[type=submit] i,
.form-button button[type=submit]:hover svg {
  -webkit-transform: scaleX(-1) rotate(45deg);
      -ms-transform: scaleX(-1) rotate(45deg);
          transform: scaleX(-1) rotate(45deg);
}

/* !END: Brand Slider CSS */
/*START: Insight CSS */
.brand-slider-1::before, .brand-slider-1::after, .brand-slider-2::before, .brand-slider-2::after {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

/* !END: Insightr CSS */
.h6-insight-chart .chart_img img {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.h6-insight_accordion .accordion_item .accordion_title {
  padding: 28px 30px 20px 65px;
}

/*START: Progress CSS */
.progress-style-2 .proggess-item .proggess-circle input {
  -webkit-margin-start: -129px;
          margin-inline-start: -129px;
}

.h4-progress .progress-style-2 .proggess-item .proggess-circle input,
.h6-tab-right .progress-style-2 .proggess-item .proggess-circle input {
  -webkit-margin-start: -107px;
          margin-inline-start: -107px;
}

/* !END: Progress CSS */
/*START: Process CSS */
.h7-process .process-item.style-4 .process-index {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.h7-process .process-item.style-4 .process-index span {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
  -webkit-margin-end: -14px;
          margin-inline-end: -14px;
}
@media (max-width: 575px) {
  .h7-process .process-item.style-4 .process-index {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .h7-process .process-item.style-4 .process-index span {
    -webkit-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
            transform: rotate(-90deg);
  }
}

.h9-process-item .step-text {
  right: auto;
  left: -82px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .h9-process-item .step-text {
    right: auto;
    left: -75px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .h9-process-item .step-text {
    right: auto;
    left: -47px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h9-process-item .step-text {
    right: auto;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
        -ms-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
  }
}

/* !END: Process CSS */
/*START: Marquee CSS */
.marquee-slider-two::before, .marquee-slider-two::after {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

/* !END: Marquee CSS */
/*START: Pricing CSS */
.pricing__box {
  -webkit-clip-path: polygon(8% 0, 0% 5%, 0% 100%, 100% 100%, 100% 0);
          clip-path: polygon(8% 0, 0% 5%, 0% 100%, 100% 100%, 100% 0);
}
.pricing__box .pricing__features-list ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.pricing__badge {
  -webkit-clip-path: polygon(100% 0, 17% 0, 0% 100%, 83% 100%);
          clip-path: polygon(100% 0, 17% 0, 0% 100%, 83% 100%);
}

.pricing__btn a span.btn-icon i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.pricing__btn a span.btn-icon i:last-child {
  -webkit-transform: translateX(150%) scaleX(-1);
      -ms-transform: translateX(150%) scaleX(-1);
          transform: translateX(150%) scaleX(-1);
}
.pricing__btn a:hover span.btn-icon i:last-child {
  -webkit-transform: translateX(0) scaleX(-1);
      -ms-transform: translateX(0) scaleX(-1);
          transform: translateX(0) scaleX(-1);
}

.pricing-card .pricing-badge {
  -webkit-clip-path: polygon(100% 0, 0% 0%, 0% 100%, 80% 100%);
          clip-path: polygon(100% 0, 0% 0%, 0% 100%, 80% 100%);
  padding: 10px 35px 10px 11px;
}
.pricing-card .pricing-right h4 {
  top: auto;
  bottom: -20px;
  -webkit-transform-origin: left;
      -ms-transform-origin: left;
          transform-origin: left;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .pricing-card .pricing-right h4 {
    top: auto;
    bottom: 30px;
  }
}

.h8-price-wrapper .price_tabs .tab_item.active .checkbox,
.h8-price-wrapper .tab_item:not(.collapsed) .checkbox,
.h8-price-mobile-wrapper .price_tabs .tab_item.active .checkbox,
.h8-price-mobile-wrapper .tab_item:not(.collapsed) .checkbox {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.h8-price-wrapper .tab_content .pricing-badge,
.h8-price-mobile-wrapper .tab_content .pricing-badge {
  -webkit-clip-path: polygon(100% 0, 0 0, 0 100%, 80% 100%);
          clip-path: polygon(100% 0, 0 0, 0 100%, 80% 100%);
  padding: 9px 35px 10px 10px;
}

/* !END: Pricing CSS */
/*START: Cta CSS */
.marquee-slider-two::before, .marquee-slider-two::after {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.tj-cta-section-3::after {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.h9-cta-wrapper .newsletter-form .form-input input:not([type=submit]):not([type=radio]):not([type=checkbox]),
.h9-cta-wrapper .newsletter-form .form-input .footer-widget .newsletter-form .form-input input[type=email] {
  padding: 18px 25px 18px 80px;
}

.h9-cta-wrapper .newsletter-form .tj-primary-btn .btn_inner {
  padding: 15px 20px 15px 43px;
}

/* !END: Cta CSS */
/*START: Blog CSS */
.h7-blog-wrapper .blog-item,
.h7-blog-wrapper .blog-item:first-child {
  padding: 15px 15px 15px 20px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h7-blog-wrapper .blog-item,
  .h7-blog-wrapper .blog-item:first-child {
    padding: 15px;
  }
}

.tj-sidebar-widget .sidebar-search form input[type=search] {
  padding: 16px 50px 16px 20px;
}

.tj-latest-comments ul .tj-comment .comment-content .comments-header .comment-text .reply {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.tj-latest-comments ul .tj-comment .comment-content .comments-header .comment-text .reply i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

blockquote cite {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}
blockquote cite::before {
  -webkit-margin-end: 0;
          margin-inline-end: 0;
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
  margin-top: 10px;
}

/* !END: Blog CSS */
/*START: Faq CSS */
.h7-faq-style .accordion-item .accordion-header .accordion-button {
  padding: 34px 30px 34px 80px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h7-faq-style .accordion-item .accordion-header .accordion-button {
    padding: 24px 15px 24px 60px;
  }
}

.tj-faq-style .accordion-item .accordion-header .accordion-button {
  padding: 25px 25px 25px 60px;
}

/* !END: Faq CSS */
/*START: Team CSS */
.team-item.style-2 .team-share {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
  padding: 12px 12px 12px 0;
}

.team-item .team-btn {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

.h5-team-item-inner .team-socials {
  -webkit-transform: translateX(50%);
      -ms-transform: translateX(50%);
          transform: translateX(50%);
}

.h6-cta-content {
  padding: 60px 60px 60px 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .h6-cta-content {
    padding: 35px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h6-cta-content {
    padding: 30px 22px;
  }
}

/* !END: Team CSS */
/*START: Feature CSS */
.feature-item.style-5 {
  padding: 60px 40px 55px 35px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .feature-item.style-5 {
    padding: 40px 30px 35px;
  }
}

.h7-feature-section .feature-item.style-4 {
  padding: 30px 25px 30px 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h7-feature-section .feature-item.style-4 {
    padding: 20px;
  }
}

.h8-feature-item.style-4 {
  padding: 40px 30px 40px 35px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .h8-feature-item.style-4 {
    padding: 20px;
  }
}

/* !END: Feature CSS */
/*START: Careers CSS */
.tj-careers .tj-careers-btn span.btn-icon i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.tj-careers .tj-careers-btn span.btn-icon i:last-child {
  -webkit-transform: scaleX(-1) translateX(-150%);
      -ms-transform: scaleX(-1) translateX(-150%);
          transform: scaleX(-1) translateX(-150%);
}
.tj-careers:hover .tj-careers-btn span.btn-icon i :first-child {
  -webkit-transform: scaleX(-1) translateX(-150%);
      -ms-transform: scaleX(-1) translateX(-150%);
          transform: scaleX(-1) translateX(-150%);
}
.tj-careers:hover .tj-careers-btn span.btn-icon i:last-child {
  -webkit-transform: scaleX(-1) translateX(0);
      -ms-transform: scaleX(-1) translateX(0);
          transform: scaleX(-1) translateX(0);
}

.tj-post__navigation .tj-nav-post__nav a span i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

/* !END: Feature CSS */
/*START: Footer CSS*/
.footer-widget .newsletter-form .form-input input:not([type=submit]):not([type=radio]):not([type=checkbox]),
.footer-widget .newsletter-form .form-input .footer-widget .newsletter-form .form-input input[type=email] {
  padding: 20px 25px 20px 80px;
}
.footer-widget .newsletter-form .form-input button i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.footer-widget .newsletter-form .form-input button:hover i {
  -webkit-transform: rotate(-40deg) scaleX(-1);
      -ms-transform: rotate(-40deg) scaleX(-1);
          transform: rotate(-40deg) scaleX(-1);
}

.newsletter-form.style-4 .form-input input:not([type=submit]):not([type=radio]):not([type=checkbox]),
.newsletter-form.style-4 .form-input .newsletter-form.style-4 .form-input input[type=email] {
  padding: 18px 25px 18px 160px;
}
.newsletter-form.style-4 .form-input button i {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}
.newsletter-form.style-4 .form-input button:hover i {
  -webkit-transform: rotate(-40deg) scaleX(-1);
      -ms-transform: rotate(-40deg) scaleX(-1);
          transform: rotate(-40deg) scaleX(-1);
}

.h7-footer-shape {
  -webkit-transform: scaleY(-1) scaleX(-1);
      -ms-transform: scaleY(-1) scaleX(-1);
          transform: scaleY(-1) scaleX(-1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .h7-footer-shape {
    inset-inline-start: -1px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .h7-footer-shape {
    inset-inline-start: -3px;
  }
}
@media (max-width: 575px) {
  .h7-footer-shape {
    inset-inline-start: -4px;
  }
}
.h7-footer-shape::after {
  inset-inline-start: 19px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .h7-footer-shape::after {
    inset-inline-start: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .h7-footer-shape::after {
    inset-inline-start: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .h7-footer-shape::after {
    inset-inline-start: 5px;
  }
}
@media (max-width: 575px) {
  .h7-footer-shape::after {
    inset-inline-start: 6px;
  }
}

.h10-footer::after {
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
}

/* !END: Footer CSS */
/*START: Animation */
@-webkit-keyframes scroll {
  from {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  to {
    -webkit-transform: translateX(calc(100% - 40px));
            transform: translateX(calc(100% - 40px));
  }
}
@keyframes scroll {
  from {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  to {
    -webkit-transform: translateX(calc(100% - 40px));
            transform: translateX(calc(100% - 40px));
  }
}
@-webkit-keyframes rotateImg {
  0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(-360deg);
            transform: translate(-50%, -50%) rotate(-360deg);
  }
}
@keyframes rotateImg {
  0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(-360deg);
            transform: translate(-50%, -50%) rotate(-360deg);
  }
}
@-webkit-keyframes rotateImg-2 {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes rotateImg-2 {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@-webkit-keyframes rotateImg-two {
  0% {
    -webkit-transform: translate(50%, -50%) rotate(0deg);
            transform: translate(50%, -50%) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(50%, -50%) rotate(-360deg);
            transform: translate(50%, -50%) rotate(-360deg);
  }
}
@keyframes rotateImg-two {
  0% {
    -webkit-transform: translate(50%, -50%) rotate(0deg);
            transform: translate(50%, -50%) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(50%, -50%) rotate(-360deg);
            transform: translate(50%, -50%) rotate(-360deg);
  }
}
@-webkit-keyframes leftRight {
  0% {
    -webkit-transform: translateX(25px);
            transform: translateX(25px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
  }
}
@keyframes leftRight {
  0% {
    -webkit-transform: translateX(25px);
            transform: translateX(25px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
  }
}
@-webkit-keyframes arrowLeftRight {
  0% {
    -webkit-transform: translate(20px, -50%) rotate(45deg);
            transform: translate(20px, -50%) rotate(45deg);
  }
  100% {
    -webkit-transform: translate(0, -50%) rotate(45deg);
            transform: translate(0, -50%) rotate(45deg);
  }
}
@keyframes arrowLeftRight {
  0% {
    -webkit-transform: translate(20px, -50%) rotate(45deg);
            transform: translate(20px, -50%) rotate(45deg);
  }
  100% {
    -webkit-transform: translate(0, -50%) rotate(45deg);
            transform: translate(0, -50%) rotate(45deg);
  }
}
@-webkit-keyframes move-two {
  0%, 100% {
    -webkit-transform: translateX(0) scaleX(-1);
            transform: translateX(0) scaleX(-1);
  }
  50% {
    -webkit-transform: translateX(-30px) scaleX(-1);
            transform: translateX(-30px) scaleX(-1);
  }
}
@keyframes move-two {
  0%, 100% {
    -webkit-transform: translateX(0) scaleX(-1);
            transform: translateX(0) scaleX(-1);
  }
  50% {
    -webkit-transform: translateX(-30px) scaleX(-1);
            transform: translateX(-30px) scaleX(-1);
  }
}
/* !END:  Animation  *//*# sourceMappingURL=main-rtl.css.map */