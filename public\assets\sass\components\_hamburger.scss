@use "../utilities" as *;

/* START: Hamburger CSS */
.hamburger {
	&-area {
		position: fixed;
		inset-inline-end: -490px;
		top: 0;
		width: 450px;
		height: 100%;
		box-shadow: -5px 0 20px -5px rgba(0, 0, 0, 0.5);
		transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
		transition-duration: 1s;
		z-index: 9999;
		-ms-overflow-style: none;
		scrollbar-width: none;
		overflow-y: scroll;
		&::-webkit-scrollbar {
			display: none;
		}
		&.opened {
			inset-inline-end: 0px;
		}
		@media #{$xs} {
			width: 320px;
		}
	}
	&_bg {
		height: 100%;
		width: 100%;
		position: absolute;
		top: 0;
		inset-inline-start: 0;
		background-color: var(--tj-color-theme-dark);
		z-index: -1;
		backdrop-filter: blur(37px);
	}
	&_wrapper {
		position: relative;
		padding: 45px 45px 90px 45px;
		overflow: auto;
		height: 100%;

		@media #{$xs} {
			padding: 40px 20px 60px 20px;
		}
	}
	&_top {
		margin-bottom: 40px;
	}
	&_close {
		&_btn {
			display: inline-block;
			font-size: 35px;
			line-height: 1;
			color: var(--tj-color-common-white);
			@media #{$xs} {
				font-size: 30px;
			}
			&:hover {
				@include transform(rotate(90deg));
				color: var(--tj-color-theme-primary);
			}
		}
	}
	&_search {
		position: relative;
		margin-bottom: 30px;
		select,
		.nice-select,
		input:not([type="submit"]):not([type="radio"]):not([type="checkbox"]),
		input {
			width: 100%;
			height: 60px;
			line-height: 60px;
			padding: 0 25px;
			background: rgba(247, 247, 247, 0.1);
			border: none;
			outline: none;
			font-size: 18px;
			color: var(--tj-color-common-white);
			&::-ms-input-placeholder {
				color: rgba(255, 255, 255, 0.6);
			}
			&::placeholder {
				color: rgba(255, 255, 255, 0.6);
			}
		}
		button {
			position: absolute;
			top: 50%;
			inset-inline-end: 25px;
			-webkit-transform: translateY(-50%);
			-moz-transform: translateY(-50%);
			-ms-transform: translateY(-50%);
			-o-transform: translateY(-50%);
			transform: translateY(-50%);
			font-size: 18px;
			color: var(--tj-color-common-white);
		}
	}
	&_menu {
		display: block;
		width: 100%;

		.mean-container {
			.mean-bar {
				background: transparent;
				box-sizing: border-box;
				margin-bottom: 30px;
				* {
					box-sizing: border-box;
				}
			}
		}
		.mean-nav {
			background: transparent;
			margin-top: 0;
			overflow: hidden;
			> ul {
				display: block !important;
				> li:first-child {
					> a {
						border-top: none;
					}
				}
			}
			ul {
				li {
					position: relative;
					a {
						color: var(--tj-color-common-white);
						width: 100%;
						padding: 18px 0;
						border-top: none;
						border-bottom: 1px solid rgba(255, 255, 255, 0.1);
						font-weight: var(--tj-fw-bold);
						font-size: 16px;
						line-height: 1;
						text-transform: capitalize;
						transition: all 0.4s ease-in-out 0s;
						&:hover {
							color: var(--tj-color-text-body);
						}
					}
					a.mean-expand {
						padding: 0;
						width: 30px;
						height: 30px;
						line-height: 1;
						display: inline-flex;
						align-items: center;
						justify-content: center;
						font-size: 1em;
						position: absolute;
						inset-inline-end: 0;
						top: 10px;
						transition: all 0.4s ease-in-out 0s;
						&:hover {
							color: var(--tj-color-common-white);
						}
						&.mean-clicked {
							color: var(--tj-color-theme-primary);
							transform: rotate(-180deg);
						}
					}

					& .sub-menu {
						li {
							padding-inline-start: 25px;
							position: relative;

							a {
								width: 100%;
								padding: 18px 0;
								text-transform: capitalize;
							}
							a.mean-expand {
								padding: 0;
								width: 30px;
							}
						}
					}

					&.dropdown-opened {
						> a {
							color: var(--tj-color-theme-primary);
						}
					}
				}
			}
		}
	}
}

.hamburger-title {
	color: var(--tj-color-common-white);
	font-size: 25px;
	margin-bottom: 20px;
	position: relative;
	z-index: 1;
}

.contact-info {
	margin-bottom: 50px;
	.contact_item {
		padding-top: 20px;
		&:first-child {
			border-top: 1px solid rgba(255, 255, 255, 0.1);
		}
		.subtitle {
			display: block;
			color: var(--tj-color-common-black-3);
			font-size: 15px;
			line-height: 1;
			margin-bottom: 8px;
		}
		.text {
			color: var(--tj-color-common-white);
			font-size: 18px;
			line-height: 1;
			a {
				display: inline-flex;
				color: inherit;
				&:hover {
					color: var(--tj-color-theme-primary);
				}
			}
		}
	}
}

.hamburger-socials {
	ul {
		margin: 0;
		padding: 0;
		list-style: none;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		gap: 15px;
		padding-top: 20px;
		border-top: 1px solid rgba(255, 255, 255, 0.1);
		li {
			a {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 35px;
				height: 35px;
				border-radius: 50%;
				color: var(--tj-color-heading-primary);
				background: rgba(247, 247, 247, 0.3);
				font-size: 16px;
				line-height: 1;
				i {
					color: var(--tj-color-heading-primary);
				}
				&:hover {
					background-color: var(--tj-color-theme-primary);
					color: var(--tj-color-common-white);
					i {
						color: var(--tj-color-common-white);
					}
				}
			}
		}
	}
}

.hamburger_logo {
	.mobile_logo {
		display: inline-block;
		max-width: 120px;
		width: 100%;
	}
}

.menu-bar {
	button {
		height: 25px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		position: relative;
		transform-origin: center center;
		transform: rotate(0deg);
		cursor: pointer;
		transition: transform 300ms ease;
		span {
			height: 3px;
			width: 35px;
			display: block;
			background: var(--tj-color-common-white);
			cursor: pointer;
			@include transition(all 0.3s ease-in-out 0s);
			margin-inline-start: auto;
			&:nth-child(2) {
				width: 40px;
				transition-delay: 200ms;
			}
			&:nth-child(3) {
				width: 30px;
			}
			&:nth-child(4) {
				position: absolute;
				top: -8px;
				inset-inline-start: 50%;
				transform: translateX(-50%);
				display: block;
				width: 3px;
				height: 0;
				transition: height 400ms;
			}
		}
	}
	&.style-2 {
		button {
			span {
				background: var(--tj-color-heading-primary);
			}
		}
	}
	&.menu-bar-toggeled {
		button {
			transform: rotate(45deg);
			transition-delay: 400ms;
			span {
				&:nth-child(1) {
					width: 0;
				}
				&:nth-child(3) {
					width: 0;
				}
				&:nth-child(4) {
					height: 40px;
					transition: height 200ms ease;
					transition-delay: 200ms;
				}
			}
		}
	}
	@media #{$md, $sm, $xs} {
		margin-inline-start: 0;
	}
}

.body-overlay {
	height: 100%;
	width: 100%;
	position: fixed;
	top: 0;
	z-index: 9999;
	inset-inline-start: 0;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease-out 0s;
	backdrop-filter: blur(10px);
	&.opened {
		opacity: 1;
		visibility: visible;
	}
}
/* !END: Hamburger CSS */
