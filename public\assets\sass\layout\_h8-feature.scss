@use "../utilities" as *;

/* START: Feature CSS */
.h8-feature {
	.container {
		--bs-gutter-x: 30px;
		.row {
			--bs-gutter-x: 30px;
			@media #{$sm, $xs} {
				--bs-gutter-x: 20px;
			}
		}

		.rg-30 {
			@media #{$sm, $xs} {
				row-gap: 20px;
			}
		}
	}
	.sec-heading {
		max-width: 360px;
		margin: 0;
		margin-inline-start: auto;

		@media #{$md} {
			margin-bottom: 50px;
			max-width: 100%;
		}
		@media #{$sm, $xs} {
			margin-bottom: 40px;
			max-width: 100%;
		}
		.desc {
			max-width: 307px;
			margin-top: 16px;
			margin-bottom: 25px;
			@media #{$md} {
				max-width: 100%;
			}
			@media #{$sm, $xs} {
				max-width: 100%;
			}
		}
	}
	&-item {
		&.style-4 {
			padding: 40px 35px 40px 30px;
			position: relative;
			display: flex;
			align-items: flex-start;
			gap: 30px;
			background-color: var(--tj-color-theme-bg);
			border: 0;
			z-index: 1;
			transition: all 0.4s;
			@media #{$lg,$md,$sm, $xs} {
				flex-wrap: wrap;
				gap: 20px;
				padding: 24px;
			}
			&::before {
				display: none;
			}
			.feature-icon {
				max-width: inherit;
				width: auto;
				margin: 0;
				flex-shrink: 0;
				position: relative;
				z-index: 1;
				svg,
				img {
					width: 72px;
					transition: filter 0.4s;
					@media #{$lg,$md,$sm, $xs} {
						width: 62px;
					}
				}
			}
			.feature-content {
				padding: 0;
				padding-top: 18px;
				@media #{$sm, $xs} {
					padding: 0;
				}
				.title {
					margin-bottom: 18px;
				}
				.desc {
					color: var(--tj-color-text-body);
				}
			}

			&:hover {
				background-color: var(--tj-color-theme-primary);
				.feature-icon {
					color: var(--tj-color-common-white);
				}
				.title {
					color: var(--tj-color-common-white);
				}
				.desc {
					color: var(--tj-color-common-white);
				}
			}
		}
	}
}
