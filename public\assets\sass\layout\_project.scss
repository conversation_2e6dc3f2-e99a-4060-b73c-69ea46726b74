@use "../utilities" as *;

/* START: Project CSS */
.tj-project-section {
	padding-top: 120px;
	padding-bottom: 120px;
	background-color: var(--tj-color-theme-bg);
	overflow: hidden;
	@media #{$lg} {
		padding-top: 100px;
		padding-bottom: 100px;
	}
	@media #{$md, $sm, $xs} {
		padding-top: 80px;
		padding-bottom: 80px;
	}
}
.tj-project-section-two {
	position: relative;
	overflow: hidden;
	background-color: var(--tj-color-theme-dark);
	z-index: 1;
	.marquee-slider-wrapper-two {
		position: absolute;
		top: 120px;
		inset-inline-start: 0;
		width: 100%;
		z-index: -1;
		@media #{$lg, $md} {
			top: 120px;
		}
		@media #{$xs} {
			display: none;
		}
	}
	.projects_button {
		margin-top: 50px;
		@media #{$sm, $md, $xs} {
			margin-top: 40px;
		}
	}
}
.project-section-three {
	background: var(--tj-color-theme-dark);
	overflow: hidden;
	.sec-heading {
		max-width: 550px;
		margin-inline-start: auto;
		margin-inline-end: auto;
		.sec-title {
			color: rgba(225, 232, 240, 1);
			letter-spacing: -0.025em;
		}
	}
}
.project-wrapper {
	position: relative;
	display: flex;
	flex-wrap: wrap;
	column-gap: 90px;
	align-items: start;
	z-index: 2;
	justify-content: space-between;
	@media #{$xl, $lg, $md} {
		column-gap: 30px;
	}
	@media #{$sm, $xs} {
		align-items: center;
		justify-content: center;
		gap: 30px;
	}
	.project-style-2 {
		&:nth-child(even) {
			margin-top: 300px;
			margin-bottom: 0;
			@media #{$lg, $md} {
				margin-top: 150px;
			}
			@media #{$sm, $xs} {
				margin-top: 0px;
			}
		}
	}
}
.project-item {
	position: relative;
	z-index: 1;
	overflow: hidden;
	&::before {
		content: "";
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		inset-inline-start: 0;
		background: linear-gradient(
			180deg,
			rgba(5, 18, 41, 0) 0%,
			var(--tj-color-theme-dark) 84%
		);
		z-index: 3;
	}
	.project-image {
		a {
			display: inline-block;
			width: 100%;
			img {
				width: 100%;
			}
		}
	}
	.project-content {
		position: absolute;
		bottom: 0;
		inset-inline-start: 0;
		width: 100%;
		padding: 15px 30px 40px 30px;
		z-index: 5;

		.project-title {
			.title {
				color: var(--tj-color-common-white);
				letter-spacing: -0.025em;
				margin-bottom: 0;

				a {
					display: inline-flex;
					align-items: start;
					position: relative;
					z-index: 1;
					padding-inline-end: 30px;

					i {
						display: inline-flex;
						line-height: 1;
						transform: translateX(-20px);
						@include transition(all 0.4s ease-in-out 0s);
						position: absolute;
						inset-inline-end: 0;
						top: 5px;
						opacity: 0;
						visibility: hidden;
					}
				}
				&:hover {
					letter-spacing: 0;
				}
			}
		}
		.tag-list {
			display: flex;
			align-items: center;
			gap: 11px;
			margin-top: 15px;

			a {
				display: inline-flex;
				color: var(--tj-color-common-white-2);
				background: rgba(247, 247, 247, 0.1);
				font-size: 14px;
				line-height: 1;
				font-weight: var(--tj-fw-regular);
				padding: 6px 10px;
				border-radius: 40px;
				&:hover {
					color: var(--tj-color-common-white);
					background-color: var(--tj-color-theme-primary);
				}
			}
		}
	}
	&:hover {
		.project-image {
			img {
				transform: scale(1.12);
			}
		}
		.project-content {
			.project-title {
				.title {
					a {
						i {
							visibility: visible;
							opacity: 1;
							transform: translateX(0);
						}
					}
				}
			}
		}
	}
	@media #{$xxl} {
		.project-content {
			padding: 15px 15px 35px 20px;
		}
	}
	@media #{$xl} {
		.project-content {
			padding: 15px 15px 35px 20px;
		}
	}
	@media #{$lg} {
		.project-content {
			padding: 15px 15px 35px 15px;
		}
	}
	@media #{$md, $sm, $xs} {
		.project-content {
			padding: 15px 15px 30px 15px;
		}
	}
}
.project-style-2 {
	max-width: 603px;
	width: 100%;
	background: rgba(247, 247, 247, 0.1);
	backdrop-filter: blur(35px);
	padding: 15px 15px;
	margin-bottom: 300px;
	@media #{$xl} {
		max-width: 543px;
	}
	@media #{$lg} {
		max-width: 450px;
		margin-bottom: 150px;
	}
	@media #{$md} {
		max-width: 332px;
		margin-bottom: 150px;
	}
	@media #{$sm, $xs} {
		margin-bottom: 0;
	}

	.project-images {
		overflow: hidden;
		position: relative;
		img {
			mix-blend-mode: luminosity;
			transition: transform 0.4s ease-in-out;
		}
		a {
			position: absolute;
			top: 50%;
			inset-inline-start: 50%;
			transform: translate(-50%, -50%) scale(0.5);
			display: inline-flex;
			align-items: center;
			justify-content: center;
			font-size: 16px;
			font-weight: var(--tj-fw-bold);
			color: var(--tj-color-common-white);
			background-color: var(--tj-color-theme-primary);
			width: 120px;
			height: 120px;
			padding: 3%;
			text-align: center;
			border-radius: 50%;
			opacity: 0;
			visibility: hidden;
			transition: transform 0.4s ease-in-out;
		}
	}
	.project-content {
		padding: 30px 15px 15px;
		.title {
			margin-bottom: 10px;
			a {
				color: var(--tj-color-common-white);
			}
			&:hover {
				letter-spacing: 0.026rem;
			}
		}
	}
	&:hover {
		.project-images {
			img {
				transform: scale(1.12);
			}
			a {
				transform: translate(-50%, -50%) scale(1);
				opacity: 1;
				visibility: visible;
			}
		}
	}
}
.project-wrapper-two {
	display: flex;
	align-items: center;
	gap: 20px;
	@media #{$md, $sm, $xs} {
		gap: 30px;
	}
}
.project-style-3 {
	position: relative;
	overflow: hidden;
	z-index: 1;
	.project-thumb {
		position: relative;
		width: 120px;
		height: 620px;
		object-fit: cover;
		border-inline-start: 1px solid rgba(39, 53, 77, 1);
		border-inline-end: 1px solid rgba(39, 53, 77, 1);
		background-color: var(--tj-color-heading-primary);
		transition: 0.6s ease-in-out;
		@media #{$xl} {
			width: 90px;
		}
		@media #{$lg} {
			width: 80px;
		}
		.image {
			opacity: 0;
			visibility: hidden;
			width: 100%;
			height: 0;
			img {
				height: 100%;
				width: 100%;
				object-fit: cover;
			}
		}
		.project-default-content {
			.title {
				position: absolute;
				inset-inline-start: 50%;
				bottom: 0;
				transform: translateX(100%) rotate(180deg);
				letter-spacing: -0.025em;
				color: var(--tj-color-common-white);
				writing-mode: vertical-lr;
				text-orientation: mixed;
				margin-bottom: 0;

				@media #{$xl,$lg,$xs} {
					transform: translateX(57%) rotate(180deg);
				}
			}
			span {
				position: absolute;
				inset-inline-start: 50%;
				top: 0;
				transform: translateX(-50%) rotate(-45deg);
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 64px;
				height: 64px;
				font-size: 28px;
				color: var(--tj-color-common-white);
				background: rgba(247, 247, 247, 0.1);
				backdrop-filter: blur(35px);
				border-radius: 50%;
			}
		}
	}
	.project-content-box {
		position: absolute;
		bottom: 30px;
		inset-inline-start: 30px;
		max-width: 380px;
		width: 100%;
		opacity: 0;
		visibility: hidden;
		padding: 30px 30px;
		transform: translateY(150%);
		transition: all 0.9s;
		background: rgba(247, 247, 247, 0.1);
		backdrop-filter: blur(35px);
		@media #{$xs} {
			inset-inline-start: 0;
			bottom: 0;
		}
		.project-tags {
			margin-bottom: 25px;
			ul {
				margin: 0;
				padding: 0;
				list-style: none;
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				column-gap: 11px;
				row-gap: 11px;
				li {
					a {
						font-size: 14px;
						background: var(--tj-color-common-white);
						backdrop-filter: blur(35px);
						color: var(--tj-color-text-body);
						padding: 3px 10px 6px;
						line-height: 1;
						border-radius: 50px;
						&:hover {
							color: var(--tj-color-common-white);
							background-color: var(--tj-color-theme-primary);
						}
					}
				}
			}
		}
		.project-text {
			.title {
				color: var(--tj-color-common-white);
				letter-spacing: -0.025em;
				margin-bottom: 10px;
				&:hover {
					letter-spacing: 0;
				}
			}
			.desc {
				color: var(--tj-color-common-white);
				p {
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
			.project-button {
				margin-top: 20px;
				.project-btn {
					display: inline-flex;
					align-items: center;
					column-gap: 5px;
					color: var(--tj-color-common-white);
					font-size: 15px;
					font-weight: var(--tj-fw-bold);
					i {
						position: relative;
						top: 1px;
					}
					&::before {
						background: var(--tj-color-common-white);
					}
				}
			}
		}
	}
	&.active {
		.project-thumb {
			width: 735px;
			border-inline-start: 1px solid transparent;
			border-inline-end: 1px solid transparent;
			@media #{$xl} {
				width: 680px;
			}
			@media #{$lg} {
				width: 535px;
			}
			.image {
				height: 100%;
				opacity: 1;
				visibility: visible;
			}
			.project-default-content {
				opacity: 0;
				visibility: hidden;
			}
		}
		.project-content-box {
			opacity: 1;
			visibility: visible;
			transform: translateY(0);
		}
	}
}
.project-category {
	ul {
		margin: 0;
		padding: 0;
		list-style: none;
		display: flex;
		flex-wrap: wrap;
		column-gap: 12px;
		row-gap: 12px;
		align-items: center;
		li {
			a {
				display: inline-block;
				color: var(--tj-color-common-white-2);
				background: rgba(247, 247, 247, 0.1);
				backdrop-filter: blur(35px);
				font-size: 14px;
				border-radius: 40px;
				padding: 6px 10px;
				line-height: 1;
				&:hover {
					color: var(--tj-color-common-white);
					background-color: var(--tj-color-theme-primary);
				}
			}
		}
	}
}
.project-wrapper-two {
	@media #{$md, $sm, $xs} {
		flex-direction: column;
	}
	.project-style-3 {
		@media #{$md, $sm, $xs} {
			width: 100%;
		}
		.project-thumb {
			@media #{$md, $sm, $xs} {
				width: 100%;
				height: 90px;
				border-inline-start: 0;
				border-inline-end: 0;
				border-top: 1px solid rgb(39, 53, 77);
				border-bottom: 1px solid rgb(39, 53, 77);
				padding-top: 10px;
			}
			.project-default-content {
				@media #{$md, $sm, $xs} {
					display: flex;
					align-items: center;
					flex-direction: row-reverse;
					justify-content: space-between;
				}
				span {
					@media #{$md, $sm, $xs} {
						position: unset;
						transform: unset;
					}
				}

				.title {
					@media #{$md, $sm, $xs} {
						position: unset;
						transform: unset;
						writing-mode: unset;
						text-orientation: unset;
					}
				}
			}
		}
		&.active {
			.project-thumb {
				@media #{$md, $sm} {
					padding-top: 0;
					height: 565px;
				}
				@media #{$xs} {
					padding-top: 0;
					height: 495px;
				}
			}
		}
	}
}
.project_catagory {
	ul {
		margin: 0;
		padding: 0;
		list-style: none;
		li {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: space-between;
			font-weight: var(--tj-fw-sbold);
			padding: 19px 0;
			position: relative;
			z-index: 1;
			border-bottom: 1px solid var(--tj-color-border-3);
			&:first-child {
				padding-top: 0;
			}
			&:last-child {
				border-bottom: 0;
				padding-bottom: 0;
			}
			span {
				color: var(--tj-color-body-text);
				&.first-child {
					min-width: 100px;
					position: relative;
					z-index: 1;
					&::before {
						position: absolute;
						content: ":";
						top: 50%;
						transform: translateY(-50%);
						inset-inline-end: 0;
					}
				}
			}
			.share-socials {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				gap: 5px;
				a {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 32px;
					height: 32px;
					border: 0;
					color: var(--tj-color-common-white);
					background-color: #aeb2b9;
					font-size: 16px;
					line-height: 1;
					border-radius: 50%;

					&:hover {
						background-color: var(--tj-color-theme-primary);
					}
				}
			}
		}
	}
	@media #{$lg} {
		ul {
			li {
				span {
					&.first-child {
						min-width: 80px;
					}
				}
			}
		}
	}
}
/* !END: Project CSS */
