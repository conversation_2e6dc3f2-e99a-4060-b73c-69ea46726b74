@use "../utilities" as *;

/* !START: Socials CSS */
.tj-socials {
  ul {
    display: flex;
    gap: 7px;
    align-content: center;
    list-style: none;
    @media #{$md,$sm,$xs} {
      gap: 5px;
    }
    li a {
      font-size: 16px;
      width: 32px;
      height: 32px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      line-height: 1;
      color: var(--tj-color-common-white);
      background: #aeb2b9;
      border-radius: 100%;
      @media #{$md,$sm,$xs} {
        font-size: 14px;
      }
      &:hover {
        background: var(--tj-color-theme-primary);
        color: var(--tj-color-common-white);
        i {
          color: var(--tj-color-common-white);
        }
      }
    }
  }
}
/* !END: Socials CSS */
